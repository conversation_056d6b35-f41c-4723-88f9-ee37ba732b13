---
title: 薛定猫API官方文档
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.30"

---

# 薛定猫API官方文档

Open AI（ChatGPT）几乎可以应用于任何涉及理解或生成自然语言或代码的任务。我们提供一系列具有不同功率级别的模型，适用于不同的任务，并且能够微调您自己的自定义模型。这些模型可用于从内容生成到语义搜索和分类的所有领域。

Base URLs:

# Authentication

# 薛定猫API官方文档/ChatGpt 接口/ChatGPT音频（Audio）

## POST 创建语音

POST /v1/audio/speech

> Body 请求参数

```json
{
  "model": "tts-1",
  "input": "The quick brown fox jumped over the lazy dog.",
  "voice": "alloy"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|body|body|object| 否 |none|
|» model|body|string| 是 |可用的 TTS 模型之一:tts-1 或 tts-1-hd|
|» input|body|string| 是 |要生成音频的文本。最大长度为4096个字符。|
|» voice|body|string| 是 |生成音频时使用的语音。支持的语音有:alloy、echo、fable、onyx、nova 和 shimmer。|
|» response_format|body|string| 否 |默认为 mp3 音频的格式。支持的格式有:mp3、opus、aac 和 flac。|
|» speed|body|number| 否 |默认为 1 生成的音频速度。选择0.25到4.0之间的值。1.0是默认值。|

> 返回示例

> 200 Response

```json
{}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

## POST 创建转录

POST /v1/audio/transcriptions

> Body 请求参数

```yaml
file: file://D:\Backup\Downloads\123.mp3
model: whisper-1
language: ""
prompt: ""
response_format: ""
temperature: 0

```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|body|body|object| 否 |none|
|» file|body|string(binary)| 是 |要转录的音频文件对象(不是文件名),格式为:flac、mp3、mp4、mpeg、mpga、m4a、ogg、wav 或 webm。|
|» model|body|string| 是 |要使用的模型 ID。目前只有 whisper-1 是可用的。|
|» language|body|string| 否 |输入音频的语言。以 ISO-639-1 格式提供输入语言可以提高准确性和延迟。|
|» prompt|body|string| 否 |一个可选的文本来指导模型的风格或继续之前的音频段落。提示应该与音频语言匹配。|
|» response_format|body|string| 否 |默认为 json|
|» temperature|body|number| 否 |默认为 0|

#### 详细说明

**» response_format**: 默认为 json
转录输出的格式,可选择:json、text、srt、verbose_json 或 vtt。

**» temperature**: 默认为 0
采样温度,between 0 和 1。更高的值像 0.8 会使输出更随机,而更低的值像 0.2 会使其更集中和确定性。如果设置为 0,模型将使用对数概率自动增加温度直到达到特定阈值。

> 返回示例

> 200 Response

```json
{
  "text": "Imagine the wildest idea that you've ever had, and you're curious about how it might scale to something that's a 100, a 1,000 times bigger. This is a place where you can get to do that."
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» text|string|true|none||none|

## POST 创建翻译

POST /v1/audio/translations

> Body 请求参数

```yaml
file: ""
model: ""
prompt: ""
response_format: ""
temperature: 0

```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|body|body|object| 否 |none|
|» file|body|string(binary)| 是 |要翻译的音频文件对象(不是文件名),格式为:flac、mp3、mp4、mpeg、mpga、m4a、ogg、wav 或 webm。|
|» model|body|string| 是 |要使用的模型 ID。目前只有 whisper-1 是可用的。|
|» prompt|body|string| 否 |一个可选的文本,用于指导模型的风格或继续之前的音频段落。提示文本应该是英文。|
|» response_format|body|string| 否 |翻译结果的格式,可选择:json、text、srt、verbose_json 或 vtt。|
|» temperature|body|number| 否 |默认为 0|

#### 详细说明

**» temperature**: 默认为 0
采样温度,介于 0 和 1 之间。更高的值如 0.8 会使输出更随机,而较低的值如 0.2 会使其更聚焦和确定性。如果设置为 0,模型将使用对数概率自动提高温度直到达到特定阈值。

> 返回示例

> 200 Response

```json
{
  "text": "Hello, my name is Wolfgang and I come from Germany. Where are you heading today?"
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» text|string|true|none||none|

# 薛定猫API官方文档/ChatGpt 接口/ChatGPT聊天（Chat）

## GET 列出模型

GET /v1/models

给定一个提示，该模型将返回一个或多个预测的完成，并且还可以返回每个位置的替代标记的概率。

为提供的提示和参数创建完成

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Content-Type|header|string| 是 |none|
|Accept|header|string| 是 |none|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{
  "id": "chatcmpl-123",
  "object": "chat.completion",
  "created": 1677652288,
  "choices": [
    {
      "index": 0,
      "message": {
        "role": "assistant",
        "content": "\n\nHello there, how may I assist you today?"
      },
      "finish_reason": "stop"
    }
  ],
  "usage": {
    "prompt_tokens": 9,
    "completion_tokens": 12,
    "total_tokens": 21
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» id|string|true|none||none|
|» object|string|true|none||none|
|» created|integer|true|none||none|
|» choices|[object]|true|none||none|
|»» index|integer|false|none||none|
|»» message|object|false|none||none|
|»»» role|string|true|none||none|
|»»» content|string|true|none||none|
|»» finish_reason|string|false|none||none|
|» usage|object|true|none||none|
|»» prompt_tokens|integer|true|none||none|
|»» completion_tokens|integer|true|none||none|
|»» total_tokens|integer|true|none||none|

# 薛定猫API官方文档/ChatGpt 接口/ChatGPT自动补全（Completions）

## POST 创建完成

POST /v1/completions

给定一个提示，该模型将返回一个或多个预测的完成，并且还可以返回每个位置的替代标记的概率。

为提供的提示和参数创建完成

> Body 请求参数

```json
{
  "model": "gpt-3.5-turbo-instruct",
  "prompt": "Say this is a test",
  "max_tokens": 7,
  "temperature": 0
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|object| 否 |none|
|» model|body|string| 是 |要使用的模型的 ID。您可以使用[List models](https://platform.openai.com/docs/api-reference/models/list) API 来查看所有可用模型，或查看我们的[模型概述](https://platform.openai.com/docs/models/overview)以了解它们的描述。|
|» prompt|body|string| 是 |生成完成的提示，编码为字符串、字符串数组、标记数组或标记数组数组。  请注意，<|endoftext|> 是模型在训练期间看到的文档分隔符，因此如果未指定提示，模型将生成新文档的开头。|
|» best_of|body|integer| 否 |默认为1 在服务器端生成best_of个补全,并返回“最佳”补全(每个令牌的日志概率最高的那个)。无法流式传输结果。  与n一起使用时,best_of控制候选补全的数量,n指定要返回的数量 – best_of必须大于n。  注意:因为这个参数会生成许多补全,所以它可以快速消耗您的令牌配额。请谨慎使用,并确保您对max_tokens和stop有合理的设置。|
|» echo|body|boolean| 否 |默认为false 除了补全之外,还回显提示|
|» frequency_penalty|body|number| 否 |默认为0 -2.0和2.0之间的数字。正值根据文本目前的现有频率处罚新令牌,降低模型逐字重复相同行的可能性。|
|» logit_bias|body|object| 否 |默认为null 修改完成中指定令牌出现的可能性。  接受一个JSON对象,该对象将令牌(由GPT令牌化器中的令牌ID指定)映射到关联偏差值,-100到100。您可以使用这个令牌化器工具(适用于GPT-2和GPT-3)将文本转换为令牌ID。从数学上讲,偏差在对模型进行采样之前添加到生成的logit中。确切效果因模型而异,但-1至1之间的值应降低或提高选择的可能性;像-100或100这样的值应导致相关令牌的禁用或专属选择。  例如,您可以传递{"50256": -100}来防止生成<|endoftext|>令牌。|
|» logprobs|body|null| 否 |默认为null|
|» max_tokens|body|integer| 否 |默认为16|
|» n|body|integer| 否 |默认为1|
|» presence_penalty|body|number| 否 |默认为0 -2.0和2.0之间的数字。正值根据它们是否出现在目前的文本中来惩罚新令牌,增加模型讨论新话题的可能性。  有关频率和存在惩罚的更多信息,请参阅。|
|» seed|body|integer| 否 |如果指定,我们的系统将尽最大努力确定性地进行采样,以便使用相同的种子和参数的重复请求应返回相同的结果。  不保证确定性,您应该参考system_fingerprint响应参数来监视后端的更改。|
|» stop|body|string| 否 |默认为null 最多4个序列,API将停止在其中生成更多令牌。返回的文本不会包含停止序列。|
|» stream|body|boolean| 否 |默认为false 是否流回部分进度。如果设置,令牌将作为可用时发送为仅数据的服务器发送事件,流由数据 Terminated by a data: [DONE] message. 对象消息终止。 Python代码示例。|
|» suffix|body|string| 否 |默认为null 在插入文本的补全之后出现的后缀。|
|» temperature|body|integer| 否 |默认为1 要使用的采样温度,介于0和2之间。更高的值(如0.8)将使输出更随机,而更低的值(如0.2)将使其更集中和确定。  我们通常建议更改这个或top_p,而不是两者都更改。|
|» user|body|string| 是 |none|
|» top_p|body|integer| 否 |表示最终用户的唯一标识符,这可以帮助OpenAI监控和检测滥用。 了解更多。|

#### 详细说明

**» logprobs**: 默认为null
包括logprobs个最可能令牌的日志概率,以及所选令牌。例如,如果logprobs为5,API将返回5个最有可能令牌的列表。 API总会返回采样令牌的logprob,因此响应中最多可能有logprobs+1个元素。

logprobs的最大值是5。

**» max_tokens**: 默认为16
在补全中生成的最大令牌数。

提示的令牌计数加上max_tokens不能超过模型的上下文长度。 计数令牌的Python代码示例。

**» n**: 默认为1
为每个提示生成的补全数量。

注意:因为这个参数会生成许多补全,所以它可以快速消耗您的令牌配额。请谨慎使用,并确保您对max_tokens和stop有合理的设置。

> 返回示例

> 200 Response

```json
{
  "id": "cmpl-uqkvlQyYK7bGYrRHQ0eXlWi7",
  "object": "text_completion",
  "created": 1589478378,
  "model": "gpt-3.5-turbo-instruct",
  "system_fingerprint": "fp_44709d6fcb",
  "choices": [
    {
      "text": "\n\nThis is indeed a test",
      "index": 0,
      "logprobs": null,
      "finish_reason": "length"
    }
  ],
  "usage": {
    "prompt_tokens": 5,
    "completion_tokens": 7,
    "total_tokens": 12
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» id|string|true|none||none|
|» object|string|true|none||none|
|» created|integer|true|none||none|
|» model|string|true|none||none|
|» system_fingerprint|string|true|none||none|
|» choices|[object]|true|none||none|
|»» text|string|false|none||none|
|»» index|integer|false|none||none|
|»» logprobs|null|false|none||none|
|»» finish_reason|string|false|none||none|
|» usage|object|true|none||none|
|»» prompt_tokens|integer|true|none||none|
|»» completion_tokens|integer|true|none||none|
|»» total_tokens|integer|true|none||none|

# 薛定猫API官方文档/ChatGpt 接口/ChatGPT嵌入（Embeddings）

## POST 创建嵌入

POST /v1/embeddings

获取给定输入的矢量表示，机器学习模型和算法可以轻松使用该表示。

相关指南：[嵌入](https://platform.openai.com/docs/guides/embeddings)

创建表示输入文本的嵌入向量。

> Body 请求参数

```json
{
  "model": "text-embedding-ada-002",
  "input": "The food was delicious and the waiter..."
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|object| 否 |none|
|» model|body|string| 是 |要使用的模型的 ID。您可以使用[List models](https://platform.openai.com/docs/api-reference/models/list) API 来查看所有可用模型，或查看我们的[模型概述](https://platform.openai.com/docs/models/overview)以了解它们的描述。|
|» input|body|string| 是 |输入文本以获取嵌入，编码为字符串或标记数组。要在单个请求中获取多个输入的嵌入，请传递一个字符串数组或令牌数组数组。每个输入的长度不得超过 8192 个标记。|

> 返回示例

> 200 Response

```json
{
  "object": "list",
  "data": [
    {
      "object": "embedding",
      "embedding": [
        0.0023064255,
        -0.009327292,
        ".... (1536 floats total for ada-002) -0.0028842222"
      ],
      "index": 0
    }
  ],
  "model": "text-embedding-ada-002",
  "usage": {
    "prompt_tokens": 8,
    "total_tokens": 8
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» object|string|true|none||none|
|» data|[object]|true|none||none|
|»» object|string|false|none||none|
|»» embedding|[number]|false|none||none|
|»» index|integer|false|none||none|
|» model|string|true|none||none|
|» usage|object|true|none||none|
|»» prompt_tokens|integer|true|none||none|
|»» total_tokens|integer|true|none||none|

# 薛定猫API官方文档/文生图接口

## POST Flux（OpenAI dall-e-3格式）

POST /v1/images/generations

[图片](https://platform.openai.com/docs/api-reference/images)

给定提示和/或输入图像，模型将生成新图像。

相关指南：[图像生成](https://platform.openai.com/docs/guides/images)

根据提示创建图像。

> Body 请求参数

```json
{
  "model": "flux",
  "prompt": "a beautiful landscape with a river and mountains",
  "size": "1024x1024",
  "n": 1
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|object| 否 |none|
|» prompt|body|string| 是 |所需图像的文本描述。最大长度为 1000 个字符。|
|» model|body|string| 否 |用于图像生成的模型。|
|» n|body|integer| 否 |要生成的图像数。必须介于 1 和 10 之间。|
|» quality|body|string| 否 |将生成的图像的质量。`hd`创建具有更精细细节和更高一致性的图像。此参数仅支持`dall-e-3`.|
|» response_format|body|string| 否 |返回生成的图像的格式。必须是 或url之一b64_json。|
|» style|body|string| 否 |生成图像的大小。必须是`256x256`、`512x512`或`1024x1024`for之一`dall-e-2`。对于模型来说，必须是`1024x1024`、`1792x1024`、 或之一。`1024x1792``dall-e-3`|
|» user|body|string| 否 |生成图像的风格。必须是 或`vivid`之一`natural`。生动使模型倾向于生成超真实和戏剧性的图像。自然使模型生成更自然、不太真实的图像。此参数仅支持`dall-e-3`.|
|» size|body|string| 否 |生成图像的大小。必须是256x256、512x512或 1024x1024之一。|

> 返回示例

> 200 Response

```json
{
  "created": 1589478378,
  "data": [
    {
      "url": "https://..."
    },
    {
      "url": "https://..."
    }
  ]
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» created|integer|true|none||none|
|» data|[object]|true|none||none|
|»» url|string|true|none||none|

## POST 创建图片编辑

POST /v1/images/edits

在给定原始图像和提示的情况下创建编辑或扩展图像。

> Body 请求参数

```yaml
image: cmMtdXBsb2FkLTE2ODc4MzMzNDc3NTEtMjA=/31225951_59371037e9_small.png
mask: []
prompt: A cute baby sea otter wearing a beret.
n: "2"
size: 1024x1024
response_format: url
user: ""

```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|object| 否 |none|
|» image|body|string(binary)| 是 |要编辑的图像。必须是有效的 PNG 文件，小于 4MB，并且是方形的。如果未提供遮罩，图像必须具有透明度，将用作遮罩。|
|» mask|body|string(binary)| 否 |附加图像，其完全透明区域（例如，alpha 为零的区域）指示image应编辑的位置。必须是有效的 PNG 文件，小于 4MB，并且尺寸与原始image相同。|
|» prompt|body|string| 是 |所需图像的文本描述。最大长度为 1000 个字符。|
|» n|body|string| 否 |要生成的图像数。必须介于 1 和 10 之间。|
|» size|body|string| 否 |生成图像的大小。必须是`256x256`、`512x512`或 `1024x1024`之一。|
|» response_format|body|string| 否 |生成的图像返回的格式。必须是`url`或`b64_json`。|
|» user|body|string| 否 |代表您的最终用户的唯一标识符，可以帮助 OpenAI 监控和检测滥用行为。[了解更多](https://platform.openai.com/docs/guides/safety-best-practices/end-user-ids)。|

> 返回示例

> 200 Response

```json
{
  "created": 1589478378,
  "data": [
    {
      "url": "https://..."
    },
    {
      "url": "https://..."
    }
  ]
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» created|integer|true|none||none|
|» data|[object]|true|none||none|
|»» url|string|true|none||none|

## POST 创建图像变体

POST /v1/images/variations

创建给定图像的变体。

> Body 请求参数

```yaml
image: cmMtdXBsb2FkLTE2ODc4MzMzNDc3NTEtMjc=/31225951_59371037e9_small.png
n: "2"
size: 1024x1024
response_format: url
user: ""

```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|object| 否 |none|
|» image|body|string(binary)| 是 |用作变体基础的图像。必须是有效的 PNG 文件，小于 4MB，并且是方形的。|
|» n|body|string| 否 |要生成的图像数。必须介于 1 和 10 之间。|
|» size|body|string| 否 |生成图像的大小。必须是`256x256`、`512x512`或 之一`1024x1024`。|
|» response_format|body|string| 否 |生成的图像返回的格式。必须是 或`url`之一`b64_json`。|
|» user|body|string| 否 |代表您的最终用户的唯一标识符，可以帮助 OpenAI 监控和检测滥用行为。[了解更多](https://platform.openai.com/docs/guides/safety-best-practices/end-user-ids)。|

> 返回示例

> 200 Response

```json
{
  "created": 1589478378,
  "data": [
    {
      "url": "https://..."
    },
    {
      "url": "https://..."
    }
  ]
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» created|integer|true|none||none|
|» data|[object]|true|none||none|
|»» url|string|true|none||none|

# 薛定猫API官方文档/文生视频模型/luma 视频生成/官方API格式

## POST 提交生成视频任务

POST /luma/generations

> Body 请求参数

```json
{
  "user_prompt": "cat dance",
  "expand_prompt": true
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Content-Type|header|string| 是 |none|
|Accept|header|string| 是 |none|
|Authorization|header|string| 是 |none|
|body|body|object| 否 |none|
|» user_prompt|body|string| 是 |必传，用户输入的提示词/问题描述|
|» expand_prompt|body|boolean| 是 |可选，提示词优化开关|
|» loop|body|boolean| 是 |可选，是否循环使用参考图|
|» image_url|body|string| 是 |可选，参考图片来源|
|» image_end_url|body|string| 是 |可选，目标关键帧图片|
|» notify_hook|body|string| 是 |可选，处理完成后的回调通知地址|

> 返回示例

> 200 Response

```json
{
  "id": "4665a07c-7641-4809-a133-10786201bb56",
  "prompt": "",
  "state": "pending",
  "queue_state": null,
  "created_at": "2024-12-22T13:38:40.139409Z",
  "batch_id": "",
  "video": null,
  "video_raw": null,
  "liked": null,
  "estimate_wait_seconds": null,
  "thumbnail": null,
  "last_frame": null
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none||none|
|» message|string|true|none||none|
|» request_id|string|true|none||none|
|» data|object|true|none||none|
|»» task_id|string|true|none||none|
|»» task_status|string|true|none||none|
|»» created_at|integer|true|none||none|
|»» updated_at|integer|true|none||none|

## POST 扩展视频

POST /luma/generations/{task_id}/extend

> Body 请求参数

```json
{
  "user_prompt": "add cat",
  "expand_prompt": true
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|task_id|path|string| 是 |task id 为需要延长的视频任务id|
|Content-Type|header|string| 是 |none|
|Accept|header|string| 是 |none|
|Authorization|header|string| 是 |none|
|body|body|object| 否 |none|
|» user_prompt|body|string| 是 |必传，用户输入的提示词/问题描述，用于生成内容的主要输入|
|» expand_prompt|body|boolean| 是 |可选，是否启用提示词优化功能|
|» image_url|body|string| 是 |可选，参考图片URL或Base64编码|
|» image_end_url|body|string| 是 |可选，关键帧图片URL或Base64编码|
|» notify_hook|body|string| 是 |可选，回调通知地址|

> 返回示例

> 200 Response

```json
{
  "id": "749d328e-4fd0-43a8-8c89-32394d60da69",
  "prompt": "",
  "state": "pending",
  "queue_state": null,
  "created_at": "2024-12-22T14:48:39.947851Z",
  "batch_id": "",
  "video": null,
  "video_raw": null,
  "liked": null,
  "estimate_wait_seconds": null,
  "thumbnail": null,
  "last_frame": null
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none||none|
|» message|string|true|none||none|
|» request_id|string|true|none||none|
|» data|object|true|none||none|
|»» task_id|string|true|none||none|
|»» task_status|string|true|none||none|
|»» created_at|integer|true|none||none|
|»» updated_at|integer|true|none||none|

# 薛定猫API官方文档/文生视频模型/luma 视频生成/查询任务

## GET 查询单个任务

GET /luma/generations/{task_id}

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|task_id|path|string| 是 |任务id|
|Content-Type|header|string| 是 |none|
|Accept|header|string| 是 |none|
|Authorization|header|string| 是 |none|

> 返回示例

> 200 Response

```json
{
  "id": "4665a07c-7641-4809-a133-10786201bb56",
  "state": "completed",
  "video": {
    "url": "https://storage.cdn-luma.com/dream-machine/b0d67582-c6f0-4973-a89c-3fea9d46d5e9/35049450-c008-4607-b1d0-0b025599f15d/video0b6619468da8e409da860706bbf26bbad.mp4",
    "width": 1360,
    "height": 752,
    "download_url": "https://storage.cdn-luma.com/dream-machine/b0d67582-c6f0-4973-a89c-3fea9d46d5e9/35049450-c008-4607-b1d0-0b025599f15d/video0b6619468da8e409da860706bbf26bbad.mp4"
  },
  "request": {
    "prompt": "cat dance",
    "aspect_ratio": "16:9"
  },
  "artifact": {
    "video": {
      "url": "https://storage.cdn-luma.com/dream-machine/b0d67582-c6f0-4973-a89c-3fea9d46d5e9/35049450-c008-4607-b1d0-0b025599f15d/video0b6619468da8e409da860706bbf26bbad.mp4",
      "width": 1360,
      "height": 752,
      "download_url": "https://storage.cdn-luma.com/dream-machine/b0d67582-c6f0-4973-a89c-3fea9d46d5e9/35049450-c008-4607-b1d0-0b025599f15d/video0b6619468da8e409da860706bbf26bbad.mp4"
    },
    "thumbnail": {
      "url": "https://imagedelivery.net/1KomXrSWiTojGGip43n0SQ/e4268de5-4a74-45ff-67b8-dc46df12de00/public",
      "width": 1360,
      "height": 752,
      "palette": {
        "grid": "em9csKqXYUgqdHVmmqeZLB8YaFpLjVw2bmFTTElBHhMNTzgodD8eVzUeTDorRSkYYEQymV80e1M0ck0ugWVRwKCApWk0hF1BgmVP"
      },
      "media_type": "image"
    },
    "video_raw": {
      "url": "https://storage.cdn-luma.com/dream-machine/b0d67582-c6f0-4973-a89c-3fea9d46d5e9/35049450-c008-4607-b1d0-0b025599f15d/video0b6619468da8e409da860706bbf26bbad.mp4",
      "width": 1360,
      "height": 752,
      "duration": 5.041667,
      "media_type": "video"
    },
    "created_at": "0001-01-01T00:00:00Z",
    "last_frame": {
      "url": "https://storage.cdn-luma.com/dream-machine/b0d67582-c6f0-4973-a89c-3fea9d46d5e9/606108d9-9daf-4265-84fb-595f18240ac5/video_0_last_frame.jpg",
      "width": 1360,
      "height": 752,
      "palette": null,
      "media_type": "image"
    }
  },
  "thumbnail": {
    "url": "https://imagedelivery.net/1KomXrSWiTojGGip43n0SQ/e4268de5-4a74-45ff-67b8-dc46df12de00/public",
    "width": 1360,
    "height": 752,
    "palette": {
      "grid": "em9csKqXYUgqdHVmmqeZLB8YaFpLjVw2bmFTTElBHhMNTzgodD8eVzUeTDorRSkYYEQymV80e1M0ck0ugWVRwKCApWk0hF1BgmVP"
    },
    "media_type": "image"
  },
  "video_raw": {
    "url": "https://storage.cdn-luma.com/dream-machine/b0d67582-c6f0-4973-a89c-3fea9d46d5e9/35049450-c008-4607-b1d0-0b025599f15d/video0b6619468da8e409da860706bbf26bbad.mp4",
    "width": 1360,
    "height": 752,
    "duration": 5.041667,
    "media_type": "video"
  },
  "created_at": "2024-12-22T13:38:40.139Z",
  "last_frame": {
    "url": "https://storage.cdn-luma.com/dream-machine/b0d67582-c6f0-4973-a89c-3fea9d46d5e9/606108d9-9daf-4265-84fb-595f18240ac5/video_0_last_frame.jpg",
    "width": 1360,
    "height": 752,
    "palette": null,
    "media_type": "image"
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» id|string|true|none||none|
|» state|string|true|none||none|
|» video|object|true|none||none|
|»» url|string|true|none||none|
|»» width|integer|true|none||none|
|»» height|integer|true|none||none|
|»» download_url|string|true|none||none|
|» request|object|true|none||none|
|»» prompt|string|true|none||none|
|»» aspect_ratio|string|true|none||none|
|» artifact|object|true|none||none|
|»» video|object|true|none||none|
|»»» url|string|true|none||none|
|»»» width|integer|true|none||none|
|»»» height|integer|true|none||none|
|»»» download_url|string|true|none||none|
|»» thumbnail|object|true|none||none|
|»»» url|string|true|none||none|
|»»» width|integer|true|none||none|
|»»» height|integer|true|none||none|
|»»» palette|object|true|none||none|
|»»»» grid|string|true|none||none|
|»»» media_type|string|true|none||none|
|»» video_raw|object|true|none||none|
|»»» url|string|true|none||none|
|»»» width|integer|true|none||none|
|»»» height|integer|true|none||none|
|»»» duration|number|true|none||none|
|»»» media_type|string|true|none||none|
|»» created_at|string|true|none||none|
|»» last_frame|object|true|none||none|
|»»» url|string|true|none||none|
|»»» width|integer|true|none||none|
|»»» height|integer|true|none||none|
|»»» palette|null|true|none||none|
|»»» media_type|string|true|none||none|
|» thumbnail|object|true|none||none|
|»» url|string|true|none||none|
|»» width|integer|true|none||none|
|»» height|integer|true|none||none|
|»» palette|object|true|none||none|
|»»» grid|string|true|none||none|
|»» media_type|string|true|none||none|
|» video_raw|object|true|none||none|
|»» url|string|true|none||none|
|»» width|integer|true|none||none|
|»» height|integer|true|none||none|
|»» duration|number|true|none||none|
|»» media_type|string|true|none||none|
|» created_at|string|true|none||none|
|» last_frame|object|true|none||none|
|»» url|string|true|none||none|
|»» width|integer|true|none||none|
|»» height|integer|true|none||none|
|»» palette|null|true|none||none|
|»» media_type|string|true|none||none|

## POST 批量获取任务

POST /luma/tasks

> Body 请求参数

```json
{
  "ids": [
    "4665a07c-7641-4809-a133-10786201bb56"
  ]
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Content-Type|header|string| 是 |none|
|Accept|header|string| 是 |none|
|Authorization|header|string| 是 |none|
|body|body|object| 否 |none|
|» model|body|string| 是 |使用的模型，可选，默认为 kling-image|
|» prompt|body|string| 是 |正向提示词，必需，描述你想要生成的图像内容，不能超过500个字符|
|» negative_prompt|body|string| 是 |负向提示词，可选，描述你不想在图像中出现的元素，不能超过200个字符|
|» image|body|string| 是 |参考图片，可选，支持 Base64 编码或图片 URL，支持 .jpg/.jpeg/.png 格式，大小不能超过 10MB|
|» image_fidelity|body|number| 是 |参考图片的影响强度，可选，取值范围：0-1，值越大，生成的图像越接近参考图片|
|» n|body|integer| 是 |生成图片的数量，可选，取值范围：1-9|
|» aspect_ratio|body|string| 是 |生成图片的纵横比，可选，可选值：16:9, 9:16, 1:1, 4:3, 3:4, 3:2, 2:3|
|» callback_url|body|string| 是 |回调通知地址，可选，当任务状态发生变化时，系统会向这个地址发送通知|

> 返回示例

> 200 Response

```json
[
  {
    "artifact": {
      "created_at": "0001-01-01T00:00:00Z",
      "last_frame": {
        "height": 752,
        "media_type": "image",
        "palette": null,
        "url": "https://storage.cdn-luma.com/dream-machine/b0d67582-c6f0-4973-a89c-3fea9d46d5e9/606108d9-9daf-4265-84fb-595f18240ac5/video_0_last_frame.jpg",
        "width": 1360
      },
      "thumbnail": {
        "height": 752,
        "media_type": "image",
        "palette": {
          "grid": "em9csKqXYUgqdHVmmqeZLB8YaFpLjVw2bmFTTElBHhMNTzgodD8eVzUeTDorRSkYYEQymV80e1M0ck0ugWVRwKCApWk0hF1BgmVP"
        },
        "url": "https://imagedelivery.net/1KomXrSWiTojGGip43n0SQ/e4268de5-4a74-45ff-67b8-dc46df12de00/public",
        "width": 1360
      },
      "video": {
        "download_url": "https://storage.cdn-luma.com/dream-machine/b0d67582-c6f0-4973-a89c-3fea9d46d5e9/35049450-c008-4607-b1d0-0b025599f15d/video0b6619468da8e409da860706bbf26bbad.mp4",
        "height": 752,
        "url": "https://storage.cdn-luma.com/dream-machine/b0d67582-c6f0-4973-a89c-3fea9d46d5e9/35049450-c008-4607-b1d0-0b025599f15d/video0b6619468da8e409da860706bbf26bbad.mp4",
        "width": 1360
      },
      "video_raw": {
        "duration": 5.041667,
        "height": 752,
        "media_type": "video",
        "url": "https://storage.cdn-luma.com/dream-machine/b0d67582-c6f0-4973-a89c-3fea9d46d5e9/35049450-c008-4607-b1d0-0b025599f15d/video0b6619468da8e409da860706bbf26bbad.mp4",
        "width": 1360
      }
    },
    "created_at": "2024-12-22T13:38:40.139Z",
    "id": "4665a07c-7641-4809-a133-10786201bb56",
    "last_frame": {
      "height": 752,
      "media_type": "image",
      "palette": null,
      "url": "https://storage.cdn-luma.com/dream-machine/b0d67582-c6f0-4973-a89c-3fea9d46d5e9/606108d9-9daf-4265-84fb-595f18240ac5/video_0_last_frame.jpg",
      "width": 1360
    },
    "request": {
      "aspect_ratio": "16:9",
      "prompt": "cat dance"
    },
    "state": "completed",
    "thumbnail": {
      "height": 752,
      "media_type": "image",
      "palette": {
        "grid": "em9csKqXYUgqdHVmmqeZLB8YaFpLjVw2bmFTTElBHhMNTzgodD8eVzUeTDorRSkYYEQymV80e1M0ck0ugWVRwKCApWk0hF1BgmVP"
      },
      "url": "https://imagedelivery.net/1KomXrSWiTojGGip43n0SQ/e4268de5-4a74-45ff-67b8-dc46df12de00/public",
      "width": 1360
    },
    "video": {
      "download_url": "https://storage.cdn-luma.com/dream-machine/b0d67582-c6f0-4973-a89c-3fea9d46d5e9/35049450-c008-4607-b1d0-0b025599f15d/video0b6619468da8e409da860706bbf26bbad.mp4",
      "height": 752,
      "url": "https://storage.cdn-luma.com/dream-machine/b0d67582-c6f0-4973-a89c-3fea9d46d5e9/35049450-c008-4607-b1d0-0b025599f15d/video0b6619468da8e409da860706bbf26bbad.mp4",
      "width": 1360
    },
    "video_raw": {
      "duration": 5.041667,
      "height": 752,
      "media_type": "video",
      "url": "https://storage.cdn-luma.com/dream-machine/b0d67582-c6f0-4973-a89c-3fea9d46d5e9/35049450-c008-4607-b1d0-0b025599f15d/video0b6619468da8e409da860706bbf26bbad.mp4",
      "width": 1360
    }
  }
]
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» artifact|object|false|none||none|
|»» created_at|string|true|none||none|
|»» last_frame|object|true|none||none|
|»»» height|integer|true|none||none|
|»»» media_type|string|true|none||none|
|»»» palette|null|true|none||none|
|»»» url|string|true|none||none|
|»»» width|integer|true|none||none|
|»» thumbnail|object|true|none||none|
|»»» height|integer|true|none||none|
|»»» media_type|string|true|none||none|
|»»» palette|object|true|none||none|
|»»»» grid|string|true|none||none|
|»»» url|string|true|none||none|
|»»» width|integer|true|none||none|
|»» video|object|true|none||none|
|»»» download_url|string|true|none||none|
|»»» height|integer|true|none||none|
|»»» url|string|true|none||none|
|»»» width|integer|true|none||none|
|»» video_raw|object|true|none||none|
|»»» duration|number|true|none||none|
|»»» height|integer|true|none||none|
|»»» media_type|string|true|none||none|
|»»» url|string|true|none||none|
|»»» width|integer|true|none||none|
|» created_at|string|false|none||none|
|» id|string|false|none||none|
|» last_frame|object|false|none||none|
|»» height|integer|true|none||none|
|»» media_type|string|true|none||none|
|»» palette|null|true|none||none|
|»» url|string|true|none||none|
|»» width|integer|true|none||none|
|» request|object|false|none||none|
|»» aspect_ratio|string|true|none||none|
|»» prompt|string|true|none||none|
|» state|string|false|none||none|
|» thumbnail|object|false|none||none|
|»» height|integer|true|none||none|
|»» media_type|string|true|none||none|
|»» palette|object|true|none||none|
|»»» grid|string|true|none||none|
|»» url|string|true|none||none|
|»» width|integer|true|none||none|
|» video|object|false|none||none|
|»» download_url|string|true|none||none|
|»» height|integer|true|none||none|
|»» url|string|true|none||none|
|»» width|integer|true|none||none|
|» video_raw|object|false|none||none|
|»» duration|number|true|none||none|
|»» height|integer|true|none||none|
|»» media_type|string|true|none||none|
|»» url|string|true|none||none|
|»» width|integer|true|none||none|

# 薛定猫API官方文档/文生视频模型/Runway 视频生成

## POST 提交视频生成任务

POST /runwayml/v1/image_to_video

> Body 请求参数

```json
{
  "promptImage": "https://www.bt.cn/bbs/template/qiao/style/image/btlogo.png",
  "model": "gen3a_turbo",
  "promptText": "cat dance",
  "watermark": false,
  "duration": 5,
  "ratio": "1280:768"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Content-Type|header|string| 是 |none|
|Accept|header|string| 是 |none|
|Authorization|header|string| 是 |none|
|body|body|object| 否 |none|
|» user_prompt|body|string| 是 |必传，用户输入的提示词/问题描述|
|» expand_prompt|body|boolean| 是 |可选，提示词优化开关|
|» loop|body|boolean| 是 |可选，是否循环使用参考图|
|» image_url|body|string| 是 |可选，参考图片来源|
|» image_end_url|body|string| 是 |可选，目标关键帧图片|
|» notify_hook|body|string| 是 |可选，处理完成后的回调通知地址|

> 返回示例

> 200 Response

```json
{
  "id": "4665a07c-7641-4809-a133-10786201bb56",
  "prompt": "",
  "state": "pending",
  "queue_state": null,
  "created_at": "2024-12-22T13:38:40.139409Z",
  "batch_id": "",
  "video": null,
  "video_raw": null,
  "liked": null,
  "estimate_wait_seconds": null,
  "thumbnail": null,
  "last_frame": null
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none||none|
|» message|string|true|none||none|
|» request_id|string|true|none||none|
|» data|object|true|none||none|
|»» task_id|string|true|none||none|
|»» task_status|string|true|none||none|
|»» created_at|integer|true|none||none|
|»» updated_at|integer|true|none||none|

## GET 查询视频任务(免费)

GET /runwayml/v1/tasks/{task_id}

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|task_id|path|string| 是 |任务id|
|Content-Type|header|string| 是 |none|
|Accept|header|string| 是 |none|
|Authorization|header|string| 是 |none|

> 返回示例

> 200 Response

```json
{
  "id": "4665a07c-7641-4809-a133-10786201bb56",
  "prompt": "",
  "state": "pending",
  "queue_state": null,
  "created_at": "2024-12-22T13:38:40.139409Z",
  "batch_id": "",
  "video": null,
  "video_raw": null,
  "liked": null,
  "estimate_wait_seconds": null,
  "thumbnail": null,
  "last_frame": null
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none||none|
|» message|string|true|none||none|
|» request_id|string|true|none||none|
|» data|object|true|none||none|
|»» task_id|string|true|none||none|
|»» task_status|string|true|none||none|
|»» created_at|integer|true|none||none|
|»» updated_at|integer|true|none||none|

# 薛定猫API官方文档/MJ绘画

## POST 提交Imagine任务

POST /mj/submit/imagine

> Body 请求参数

```json
{
  "base64Array": [],
  "notifyHook": "",
  "prompt": "cat --v 6.0",
  "state": "",
  "botType": "MID_JOURNEY"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|object| 否 |none|
|» botType|body|string| 是 |bot类型，mj(默认)或niji|
|» prompt|body|string| 是 |提示词|
|» base64Array|body|[string]| 否 |垫图base64数组|
|» notifyHook|body|string| 否 |回调地址, 为空时使用全局notifyHook|
|» state|body|string| 否 |自定义参数|

#### 枚举值

|属性|值|
|---|---|
|» botType|MID_JOURNEY|
|» botType|NIJI_JOURNEY|

> 返回示例

> 200 Response

```json
{
  "code": 1,
  "description": "Submit success",
  "result": "1730621718151844",
  "//任务id \"properties\"": {
    "discordChannelId": "1300842676874379336",
    "discordInstanceId": "1572398367386955776"
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

## GET 根据任务ID 查询任务状态

GET /mj/task/1730621826053455/fetch

> Body 请求参数

```yaml
{}

```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|object| 否 |none|

> 返回示例

```json
{
  "id": "1730621826053455",
  "action": "IMAGINE",
  "customId": "",
  "botType": "",
  "prompt": "pig --v 6.1",
  "promptEn": "pig --v 6.1",
  "description": "Submit success",
  "state": "",
  "submitTime": 1730621826053,
  "startTime": 1730621828024,
  "finishTime": 1730621855817,
  "imageUrl": "https://cdnmjp.oneabc.org/attachments/1300842274347028520/1302547211321741343/kennedyhernandez46715_74108_pig_3785da15-4f70-4034-9128-f3ff1ac634fa.png?ex=6728831f&is=6727319f&hm=f6d701914d608e4da9298d2290b3616317264a70635fbf08a37ca6c1bb671b50&",
  "status": "SUCCESS",
  "progress": "100%",
  "failReason": "",
  "buttons": [
    {
      "customId": "MJ::JOB::upsample::1::3785da15-4f70-4034-9128-f3ff1ac634fa",
      "emoji": "",
      "label": "U1",
      "type": 2,
      "style": 2
    },
    {
      "customId": "MJ::JOB::upsample::2::3785da15-4f70-4034-9128-f3ff1ac634fa",
      "emoji": "",
      "label": "U2",
      "type": 2,
      "style": 2
    },
    {
      "customId": "MJ::JOB::upsample::3::3785da15-4f70-4034-9128-f3ff1ac634fa",
      "emoji": "",
      "label": "U3",
      "type": 2,
      "style": 2
    },
    {
      "customId": "MJ::JOB::upsample::4::3785da15-4f70-4034-9128-f3ff1ac634fa",
      "emoji": "",
      "label": "U4",
      "type": 2,
      "style": 2
    },
    {
      "customId": "MJ::JOB::reroll::0::3785da15-4f70-4034-9128-f3ff1ac634fa::SOLO",
      "emoji": "🔄",
      "label": "",
      "type": 2,
      "style": 2
    },
    {
      "customId": "MJ::JOB::variation::1::3785da15-4f70-4034-9128-f3ff1ac634fa",
      "emoji": "",
      "label": "V1",
      "type": 2,
      "style": 2
    },
    {
      "customId": "MJ::JOB::variation::2::3785da15-4f70-4034-9128-f3ff1ac634fa",
      "emoji": "",
      "label": "V2",
      "type": 2,
      "style": 2
    },
    {
      "customId": "MJ::JOB::variation::3::3785da15-4f70-4034-9128-f3ff1ac634fa",
      "emoji": "",
      "label": "V3",
      "type": 2,
      "style": 2
    },
    {
      "customId": "MJ::JOB::variation::4::3785da15-4f70-4034-9128-f3ff1ac634fa",
      "emoji": "",
      "label": "V4",
      "type": 2,
      "style": 2
    }
  ],
  "maskBase64": "",
  "properties": {
    "finalPrompt": "pig --v 6.1",
    "finalZhPrompt": ""
  }
}
```

```json
{
  "id": "1730621826053455",
  "action": "IMAGINE",
  "customId": "",
  "botType": "",
  "prompt": "pig --v 6.1",
  "promptEn": "pig --v 6.1",
  "description": "Submit success",
  "state": "",
  "submitTime": 1730621826053,
  "startTime": 1730621828024,
  "finishTime": 1730621855817,
  "imageUrl": "https://cdnmjp.oneabc.org/attachments/1300842274347028520/1302547211321741343/kennedyhernandez46715_74108_pig_3785da15-4f70-4034-9128-f3ff1ac634fa.png?ex=6728831f&is=6727319f&hm=f6d701914d608e4da9298d2290b3616317264a70635fbf08a37ca6c1bb671b50&",
  "status": "SUCCESS",
  "progress": "100%",
  "failReason": "",
  "buttons": [
    {
      "customId": "MJ::JOB::upsample::1::3785da15-4f70-4034-9128-f3ff1ac634fa",
      "emoji": "",
      "label": "U1",
      "type": 2,
      "style": 2
    },
    {
      "customId": "MJ::JOB::upsample::2::3785da15-4f70-4034-9128-f3ff1ac634fa",
      "emoji": "",
      "label": "U2",
      "type": 2,
      "style": 2
    },
    {
      "customId": "MJ::JOB::upsample::3::3785da15-4f70-4034-9128-f3ff1ac634fa",
      "emoji": "",
      "label": "U3",
      "type": 2,
      "style": 2
    },
    {
      "customId": "MJ::JOB::upsample::4::3785da15-4f70-4034-9128-f3ff1ac634fa",
      "emoji": "",
      "label": "U4",
      "type": 2,
      "style": 2
    },
    {
      "customId": "MJ::JOB::reroll::0::3785da15-4f70-4034-9128-f3ff1ac634fa::SOLO",
      "emoji": "🔄",
      "label": "",
      "type": 2,
      "style": 2
    },
    {
      "customId": "MJ::JOB::variation::1::3785da15-4f70-4034-9128-f3ff1ac634fa",
      "emoji": "",
      "label": "V1",
      "type": 2,
      "style": 2
    },
    {
      "customId": "MJ::JOB::variation::2::3785da15-4f70-4034-9128-f3ff1ac634fa",
      "emoji": "",
      "label": "V2",
      "type": 2,
      "style": 2
    },
    {
      "customId": "MJ::JOB::variation::3::3785da15-4f70-4034-9128-f3ff1ac634fa",
      "emoji": "",
      "label": "V3",
      "type": 2,
      "style": 2
    },
    {
      "customId": "MJ::JOB::variation::4::3785da15-4f70-4034-9128-f3ff1ac634fa",
      "emoji": "",
      "label": "V4",
      "type": 2,
      "style": 2
    }
  ],
  "maskBase64": "",
  "properties": {
    "finalPrompt": "pig --v 6.1",
    "finalZhPrompt": ""
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» id|string|true|none||none|
|» action|string|true|none||none|
|» customId|string|true|none||none|
|» botType|string|true|none||none|
|» prompt|string|true|none||none|
|» promptEn|string|true|none||none|
|» description|string|true|none||none|
|» state|string|true|none||none|
|» submitTime|integer|true|none||none|
|» startTime|integer|true|none||none|
|» finishTime|integer|true|none||none|
|» imageUrl|string|true|none||none|
|» status|string|true|none||none|
|» progress|string|true|none||none|
|» failReason|string|true|none||none|
|» buttons|[object]|true|none||none|
|»» customId|string|true|none||none|
|»» emoji|string|true|none||none|
|»» label|string|true|none||none|
|»» type|integer|true|none||none|
|»» style|integer|true|none||none|
|» maskBase64|string|true|none||none|
|» properties|object|true|none||none|
|»» finalPrompt|string|true|none||none|
|»» finalZhPrompt|string|true|none||none|

## POST 根据ID列表查询任务

POST /mj/task/list-by-condition

> Body 请求参数

```json
{
  "ids": [
    "1730621826053455"
  ]
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|object| 否 |none|
|» ids|body|[string]| 是 |none|

> 返回示例

```json
{
  "id": "1730621826053455",
  "action": "IMAGINE",
  "customId": "",
  "botType": "",
  "prompt": "pig --v 6.1",
  "promptEn": "pig --v 6.1",
  "description": "Submit success",
  "state": "",
  "submitTime": 1730621826053,
  "startTime": 1730621828024,
  "finishTime": 1730621855817,
  "imageUrl": "https://cdnmjp.oneabc.org/attachments/1300842274347028520/1302547211321741343/kennedyhernandez46715_74108_pig_3785da15-4f70-4034-9128-f3ff1ac634fa.png?ex=6728831f&is=6727319f&hm=f6d701914d608e4da9298d2290b3616317264a70635fbf08a37ca6c1bb671b50&",
  "status": "SUCCESS",
  "progress": "100%",
  "failReason": "",
  "buttons": [
    {
      "customId": "MJ::JOB::upsample::1::3785da15-4f70-4034-9128-f3ff1ac634fa",
      "emoji": "",
      "label": "U1",
      "type": 2,
      "style": 2
    },
    {
      "customId": "MJ::JOB::upsample::2::3785da15-4f70-4034-9128-f3ff1ac634fa",
      "emoji": "",
      "label": "U2",
      "type": 2,
      "style": 2
    },
    {
      "customId": "MJ::JOB::upsample::3::3785da15-4f70-4034-9128-f3ff1ac634fa",
      "emoji": "",
      "label": "U3",
      "type": 2,
      "style": 2
    },
    {
      "customId": "MJ::JOB::upsample::4::3785da15-4f70-4034-9128-f3ff1ac634fa",
      "emoji": "",
      "label": "U4",
      "type": 2,
      "style": 2
    },
    {
      "customId": "MJ::JOB::reroll::0::3785da15-4f70-4034-9128-f3ff1ac634fa::SOLO",
      "emoji": "🔄",
      "label": "",
      "type": 2,
      "style": 2
    },
    {
      "customId": "MJ::JOB::variation::1::3785da15-4f70-4034-9128-f3ff1ac634fa",
      "emoji": "",
      "label": "V1",
      "type": 2,
      "style": 2
    },
    {
      "customId": "MJ::JOB::variation::2::3785da15-4f70-4034-9128-f3ff1ac634fa",
      "emoji": "",
      "label": "V2",
      "type": 2,
      "style": 2
    },
    {
      "customId": "MJ::JOB::variation::3::3785da15-4f70-4034-9128-f3ff1ac634fa",
      "emoji": "",
      "label": "V3",
      "type": 2,
      "style": 2
    },
    {
      "customId": "MJ::JOB::variation::4::3785da15-4f70-4034-9128-f3ff1ac634fa",
      "emoji": "",
      "label": "V4",
      "type": 2,
      "style": 2
    }
  ],
  "maskBase64": "",
  "properties": {
    "finalPrompt": "pig --v 6.1",
    "finalZhPrompt": ""
  }
}
```

```json
[
  {
    "id": "1730621826053455",
    "action": "IMAGINE",
    "customId": "",
    "botType": "",
    "prompt": "pig --v 6.1",
    "promptEn": "pig --v 6.1",
    "description": "Submit success",
    "state": "",
    "submitTime": 1730621826053,
    "startTime": 1730621828024,
    "finishTime": 1730621855817,
    "imageUrl": "https://cdnmjp.oneabc.org/attachments/1300842274347028520/1302547211321741343/kennedyhernandez46715_74108_pig_3785da15-4f70-4034-9128-f3ff1ac634fa.png?ex=6728831f&is=6727319f&hm=f6d701914d608e4da9298d2290b3616317264a70635fbf08a37ca6c1bb671b50&",
    "status": "SUCCESS",
    "progress": "100%",
    "failReason": "",
    "buttons": [
      {
        "customId": "MJ::JOB::upsample::1::3785da15-4f70-4034-9128-f3ff1ac634fa",
        "emoji": "",
        "label": "U1",
        "type": 2,
        "style": 2
      },
      {
        "customId": "MJ::JOB::upsample::2::3785da15-4f70-4034-9128-f3ff1ac634fa",
        "emoji": "",
        "label": "U2",
        "type": 2,
        "style": 2
      },
      {
        "customId": "MJ::JOB::upsample::3::3785da15-4f70-4034-9128-f3ff1ac634fa",
        "emoji": "",
        "label": "U3",
        "type": 2,
        "style": 2
      },
      {
        "customId": "MJ::JOB::upsample::4::3785da15-4f70-4034-9128-f3ff1ac634fa",
        "emoji": "",
        "label": "U4",
        "type": 2,
        "style": 2
      },
      {
        "customId": "MJ::JOB::reroll::0::3785da15-4f70-4034-9128-f3ff1ac634fa::SOLO",
        "emoji": "🔄",
        "label": "",
        "type": 2,
        "style": 2
      },
      {
        "customId": "MJ::JOB::variation::1::3785da15-4f70-4034-9128-f3ff1ac634fa",
        "emoji": "",
        "label": "V1",
        "type": 2,
        "style": 2
      },
      {
        "customId": "MJ::JOB::variation::2::3785da15-4f70-4034-9128-f3ff1ac634fa",
        "emoji": "",
        "label": "V2",
        "type": 2,
        "style": 2
      },
      {
        "customId": "MJ::JOB::variation::3::3785da15-4f70-4034-9128-f3ff1ac634fa",
        "emoji": "",
        "label": "V3",
        "type": 2,
        "style": 2
      },
      {
        "customId": "MJ::JOB::variation::4::3785da15-4f70-4034-9128-f3ff1ac634fa",
        "emoji": "",
        "label": "V4",
        "type": 2,
        "style": 2
      }
    ],
    "maskBase64": "",
    "properties": {
      "finalPrompt": "pig --v 6.1",
      "finalZhPrompt": ""
    }
  }
]
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» id|string|false|none||none|
|» action|string|false|none||none|
|» customId|string|false|none||none|
|» botType|string|false|none||none|
|» prompt|string|false|none||none|
|» promptEn|string|false|none||none|
|» description|string|false|none||none|
|» state|string|false|none||none|
|» submitTime|integer|false|none||none|
|» startTime|integer|false|none||none|
|» finishTime|integer|false|none||none|
|» imageUrl|string|false|none||none|
|» status|string|false|none||none|
|» progress|string|false|none||none|
|» failReason|string|false|none||none|
|» buttons|[object]|false|none||none|
|»» customId|string|true|none||none|
|»» emoji|string|true|none||none|
|»» label|string|true|none||none|
|»» type|integer|true|none||none|
|»» style|integer|true|none||none|
|» maskBase64|string|false|none||none|
|» properties|object|false|none||none|
|»» finalPrompt|string|true|none||none|
|»» finalZhPrompt|string|true|none||none|

## GET 获取任务图片的seed

GET /mj/task/{id}/image-seed

> Body 请求参数

```yaml
ids:
  - "1231234123"
  - "456456456"

```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|path|string| 是 |none|
|Authorization|header|string| 否 |none|
|body|body|object| 否 |none|
|» ids|body|[string]| 否 |none|

> 返回示例

> 200 Response

```json
{
  "id": "1730621826053455",
  "action": "IMAGINE",
  "customId": "",
  "botType": "",
  "prompt": "pig --v 6.1",
  "promptEn": "pig --v 6.1",
  "description": "Submit success",
  "state": "",
  "submitTime": 1730621826053,
  "startTime": 1730621828024,
  "finishTime": 1730621855817,
  "imageUrl": "https://cdnmjp.oneabc.org/attachments/1300842274347028520/1302547211321741343/kennedyhernandez46715_74108_pig_3785da15-4f70-4034-9128-f3ff1ac634fa.png?ex=6728831f&is=6727319f&hm=f6d701914d608e4da9298d2290b3616317264a70635fbf08a37ca6c1bb671b50&",
  "status": "SUCCESS",
  "progress": "100%",
  "failReason": "",
  "buttons": [
    {
      "customId": "MJ::JOB::upsample::1::3785da15-4f70-4034-9128-f3ff1ac634fa",
      "emoji": "",
      "label": "U1",
      "type": 2,
      "style": 2
    },
    {
      "customId": "MJ::JOB::upsample::2::3785da15-4f70-4034-9128-f3ff1ac634fa",
      "emoji": "",
      "label": "U2",
      "type": 2,
      "style": 2
    },
    {
      "customId": "MJ::JOB::upsample::3::3785da15-4f70-4034-9128-f3ff1ac634fa",
      "emoji": "",
      "label": "U3",
      "type": 2,
      "style": 2
    },
    {
      "customId": "MJ::JOB::upsample::4::3785da15-4f70-4034-9128-f3ff1ac634fa",
      "emoji": "",
      "label": "U4",
      "type": 2,
      "style": 2
    },
    {
      "customId": "MJ::JOB::reroll::0::3785da15-4f70-4034-9128-f3ff1ac634fa::SOLO",
      "emoji": "🔄",
      "label": "",
      "type": 2,
      "style": 2
    },
    {
      "customId": "MJ::JOB::variation::1::3785da15-4f70-4034-9128-f3ff1ac634fa",
      "emoji": "",
      "label": "V1",
      "type": 2,
      "style": 2
    },
    {
      "customId": "MJ::JOB::variation::2::3785da15-4f70-4034-9128-f3ff1ac634fa",
      "emoji": "",
      "label": "V2",
      "type": 2,
      "style": 2
    },
    {
      "customId": "MJ::JOB::variation::3::3785da15-4f70-4034-9128-f3ff1ac634fa",
      "emoji": "",
      "label": "V3",
      "type": 2,
      "style": 2
    },
    {
      "customId": "MJ::JOB::variation::4::3785da15-4f70-4034-9128-f3ff1ac634fa",
      "emoji": "",
      "label": "V4",
      "type": 2,
      "style": 2
    }
  ],
  "maskBase64": "",
  "properties": {
    "finalPrompt": "pig --v 6.1",
    "finalZhPrompt": ""
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» id|string|true|none||none|
|» action|string|true|none||none|
|» customId|string|true|none||none|
|» botType|string|true|none||none|
|» prompt|string|true|none||none|
|» promptEn|string|true|none||none|
|» description|string|true|none||none|
|» state|string|true|none||none|
|» submitTime|integer|true|none||none|
|» startTime|integer|true|none||none|
|» finishTime|integer|true|none||none|
|» imageUrl|string|true|none||none|
|» status|string|true|none||none|
|» progress|string|true|none||none|
|» failReason|string|true|none||none|
|» buttons|[object]|true|none||none|
|»» customId|string|true|none||none|
|»» emoji|string|true|none||none|
|»» label|string|true|none||none|
|»» type|integer|true|none||none|
|»» style|integer|true|none||none|
|» maskBase64|string|true|none||none|
|» properties|object|true|none||none|
|»» finalPrompt|string|true|none||none|
|»» finalZhPrompt|string|true|none||none|

## POST 提交Blend任务

POST /mj/submit/action

> Body 请求参数

```json
{
  "botType": "MID_JOURNEY",
  "base64Array": [
    "data:image/png;base64,xxx1",
    "data:image/png;base64,xxx2"
  ],
  "dimensions": "SQUARE",
  "notifyHook": "",
  "state": ""
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|object| 否 |none|
|» botType|body|string| 是 |bot类型，mj(默认)或niji|
|» base64Array|body|string| 否 |图片base64数组|
|» dimensions|body|string| 否 |比例: PORTRAIT(2:3); SQUARE(1:1); LANDSCAPE(3:2)|
|» quality|body|string| 否 |将生成的图像的质量。`hd`创建具有更精细细节和更高一致性的图像。此参数仅支持`dall-e-3`.|
|» notifyHook|body|string| 否 |回调地址, 为空时使用全局notifyHook|
|» state|body|string| 否 |自定义参数|

#### 枚举值

|属性|值|
|---|---|
|» botType|NIJI_JOURNEY|
|» botType|MID_JOURNEY|
|» dimensions|PORTRAIT|
|» dimensions|SQUARE|
|» dimensions|LANDSCAPE|

> 返回示例

> 200 Response

```json
{
  "created": 1589478378,
  "data": [
    {
      "url": "https://..."
    },
    {
      "url": "https://..."
    }
  ]
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» created|integer|true|none||none|
|» data|[object]|true|none||none|
|»» url|string|true|none||none|

## POST 提交Describe任务

POST /mj/submit/describe

> Body 请求参数

```json
{
  "botType": "MID_JOURNEY",
  "base64": "data:image/png;base64,xxx",
  "notifyHook": "",
  "state": ""
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|object| 否 |none|
|» botType|body|string| 是 |bot类型，mj(默认)或niji|
|» base64|body|string| 否 |用于图像生成的模型。|
|» notifyHook|body|integer| 否 |要生成的图像数。必须介于 1 和 10 之间。|
|» state|body|string| 否 |将生成的图像的质量。`hd`创建具有更精细细节和更高一致性的图像。此参数仅支持`dall-e-3`.|

#### 枚举值

|属性|值|
|---|---|
|» botType|MID_JOURNEY|
|» botType|NIJI_JOURNEY|

> 返回示例

> 200 Response

```json
{
  "created": 1589478378,
  "data": [
    {
      "url": "https://..."
    },
    {
      "url": "https://..."
    }
  ]
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» created|integer|true|none||none|
|» data|[object]|true|none||none|
|»» url|string|true|none||none|

## POST 提交Shorten任务

POST /mj/submit/shorten

> Body 请求参数

```json
{
  "botType": "MID_JOURNEY",
  "prompt": "Cat",
  "notifyHook": "",
  "state": ""
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|object| 否 |none|
|» botType|body|string| 是 |bot类型，mj(默认)或niji|
|» prompt|body|string| 否 |提示词|
|» notifyHook|body|integer| 否 |回调地址, 为空时使用全局notifyHook|
|» state|body|string| 否 |自定义参数|

> 返回示例

> 200 Response

```json
{
  "created": 1589478378,
  "data": [
    {
      "url": "https://..."
    },
    {
      "url": "https://..."
    }
  ]
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» created|integer|true|none||none|
|» data|[object]|true|none||none|
|»» url|string|true|none||none|

## POST 提交Modal

POST /mj/submit/modal

> Body 请求参数

```json
{
  "maskBase64": "",
  "prompt": "",
  "taskId": "14001934816969359"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|object| 否 |none|
|» maskBase64|body|string| 是 |局部重绘的蒙版base64|
|» prompt|body|string| 否 |提示词|
|» taskId|body|integer| 否 |任务ID|

> 返回示例

> 200 Response

```json
{
  "created": 1589478378,
  "data": [
    {
      "url": "https://..."
    },
    {
      "url": "https://..."
    }
  ]
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» created|integer|true|none||none|
|» data|[object]|true|none||none|
|»» url|string|true|none||none|

## POST 提交swap_face任务

POST /mj/insight-face/swap

> Body 请求参数

```json
{
  "sourceBase64": "data:image/png;base64,xxx1",
  "targetBase64": "data:image/png;base64,xxx2",
  "notifyHook": "string",
  "state": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|object| 否 |none|
|» sourceBase64|body|string| 是 |人脸源图片base64|
|» targetBase64|body|string| 否 |目标图片base64|
|» notifyHook|body|integer| 否 |回调地址, 为空时使用全局notifyHook|
|» state|body|string| 否 |自定义参数|

> 返回示例

> 200 Response

```json
{
  "created": 1589478378,
  "data": [
    {
      "url": "https://..."
    },
    {
      "url": "https://..."
    }
  ]
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» created|integer|true|none||none|
|» data|[object]|true|none||none|
|»» url|string|true|none||none|

# 薛定猫API官方文档/快手可灵

## POST 图像生成

POST /kling/v1/images/generations

> Body 请求参数

```json
{
  "model": "kling-image",
  "prompt": "在海滩上度假的快乐场景。"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Content-Type|header|string| 是 |none|
|Accept|header|string| 是 |none|
|Authorization|header|string| 是 |none|
|body|body|object| 否 |none|
|» model|body|string| 是 |使用的模型，可选，默认为 kling-image|
|» prompt|body|string| 是 |正向提示词，必需，描述你想要生成的图像内容，不能超过500个字符|
|» negative_prompt|body|string| 是 |负向提示词，可选，描述你不想在图像中出现的元素，不能超过200个字符|
|» image|body|string| 是 |参考图片，可选，支持 Base64 编码或图片 URL，支持 .jpg/.jpeg/.png 格式，大小不能超过 10MB|
|» image_fidelity|body|number| 是 |参考图片的影响强度，可选，取值范围：0-1，值越大，生成的图像越接近参考图片|
|» n|body|integer| 是 |生成图片的数量，可选，取值范围：1-9|
|» aspect_ratio|body|string| 是 |生成图片的纵横比，可选，可选值：16:9, 9:16, 1:1, 4:3, 3:4, 3:2, 2:3|
|» callback_url|body|string| 是 |回调通知地址，可选，当任务状态发生变化时，系统会向这个地址发送通知|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "message": "SUCCEED",
  "request_id": "CjMT7WdSwWcAAAAAALvB3g",
  "data": {
    "task_id": "CjMT7WdSwWcAAAAAALvB3g",
    "task_status": "submitted",
    "created_at": 1733851336696,
    "updated_at": 1733851336696
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none||none|
|» message|string|true|none||none|
|» request_id|string|true|none||none|
|» data|object|true|none||none|
|»» task_id|string|true|none||none|
|»» task_status|string|true|none||none|
|»» created_at|integer|true|none||none|
|»» updated_at|integer|true|none||none|

## POST 文生视频

POST /kling/v1/videos/text2video

> Body 请求参数

```json
{
  "model": "kling-video_std_5",
  "prompt": "在海滩上度假的快乐场景。"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Content-Type|header|string| 是 |none|
|Accept|header|string| 是 |none|
|Authorization|header|string| 是 |none|
|body|body|object| 否 |none|
|» model|body|string| 是 |模型名称，可选参数|
|» prompt|body|string| 是 |正向文本提示，必需，描述想要生成的视频内容，不超过500字符|
|» negative_prompt|body|string| 是 |负向文本提示，可选，描述不想在视频中出现的元素，不超过200字符|
|» cfg_scale|body|number| 是 |生成视频的自由度，可选，取值范围0-1，值越大相关性越强|
|» mode|body|string| 是 |生成视频的模式，可选，std（高性能）或 pro（高表现）|
|» camera_control|body|object| 是 |控制摄像机运动的协议，可选，未指定则智能匹配|
|»» type|body|string| 是 |摄像机控制类型|
|»» config|body|object| 是 |摄像机运动配置，包含六个控制参数|
|»»» horizontal|body|integer| 是 |水平移动|
|»»» vertical|body|integer| 是 |垂直移动|
|»»» pan|body|integer| 是 |平移|
|»»» tilt|body|integer| 是 |倾斜|
|»»» roll|body|integer| 是 |旋转|
|»»» zoom|body|integer| 是 |缩放|
|» aspect_ratio|body|string| 是 |生成视频的画面纵横比，可选值：16:9, 9:16, 1:1|
|» duration|body|string| 是 |生成视频时长，单位秒，可选值：5或10|
|» callback_url|body|string| 是 |回调通知地址，可选，用于接收任务状态更新|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "message": "SUCCEED",
  "request_id": "CjiRI2dSwzkAAAAAAK6fwA",
  "data": {
    "task_id": "CjiRI2dSwzkAAAAAAK6fwA",
    "task_status": "submitted",
    "created_at": 1733824167170,
    "updated_at": 1733824167170
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» id|string|true|none||none|
|» object|string|true|none||none|
|» created|integer|true|none||none|
|» choices|[object]|true|none||none|
|»» index|integer|false|none||none|
|»» message|object|false|none||none|
|»»» role|string|true|none||none|
|»»» content|string|true|none||none|
|»» finish_reason|string|false|none||none|
|» usage|object|true|none||none|
|»» prompt_tokens|integer|true|none||none|
|»» completion_tokens|integer|true|none||none|
|»» total_tokens|integer|true|none||none|

## POST 图生视频

POST /kling/v1/videos/image2video

> Body 请求参数

```json
{
  "prompt": "flow",
  "negative_prompt": "",
  "image": "https://img.jpg",
  "image_tail": "",
  "aspect_ratio": "1:1",
  "mode": "std",
  "duration": "5"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Content-Type|header|string| 是 |none|
|Accept|header|string| 是 |none|
|Authorization|header|string| 是 |none|
|body|body|object| 否 |none|
|» model|body|string| 是 |模型名称，可选参数|
|» image|body|string| 是 |参考图像，必需，支持Base64编码或图片URL，支持.jpg/.jpeg/.png格式，大小不超过10MB，分辨率不小于300*300px|
|» image_tail|body|string| 是 |参考图像-尾帧控制，可选，支持Base64编码或图片URL，支持.jpg/.jpeg/.png格式，大小不超过10MB，分辨率不小于300*300px|
|» prompt|body|string| 是 |正向文本提示，可选，描述想要生成的视频内容，不超过5000字符|
|» negative_prompt|body|string| 是 |负向文本提示，可选，描述不想在视频中出现的元素，不超过2000字符|
|» cfg_scale|body|number| 是 |生成视频的自由度，可选，取值范围0-1，值越大相关性越强|
|» mode|body|string| 是 |生成视频的模式，可选，std（高性能）或 pro（高表现）|
|» duration|body|string| 是 |生成视频时长，单位秒，可选值：5或10（注意：包含尾帧的请求仅支持5秒）|
|» callback_url|body|string| 是 |回调通知地址，可选，用于接收任务状态更新|

> 返回示例

> 200 Response

```json
{}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

## GET 查询任务(免费)

GET /kling/v1/images/generations/CjMT7WdSwWcAAAAAALvB3g

> Body 请求参数

```json
{}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Content-Type|header|string| 是 |none|
|Accept|header|string| 是 |none|
|Authorization|header|string| 是 |none|
|body|body|object| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "data": {
    "task_id": "CjMT7WdSwWcAAAAAALvB3g",
    "created_at": 1733851336696,
    "updated_at": 1733851344553,
    "task_result": {
      "images": [
        {
          "id": "",
          "url": "https://cdn.klingai.com/bs2/upload-kling-api/0923471513/image/CjMT7WdSwWcAAAAAALvB3g-0_raw_image_0.png"
        }
      ]
    },
    "task_status": "succeed",
    "task_status_msg": ""
  },
  "message": "SUCCEED",
  "request_id": "CjNTkGdSwxYAAAAAALud5A"
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» id|string|true|none||none|
|» object|string|true|none||none|
|» created|integer|true|none||none|
|» choices|[object]|true|none||none|
|»» index|integer|false|none||none|
|»» message|object|false|none||none|
|»»» role|string|true|none||none|
|»»» content|string|true|none||none|
|»» finish_reason|string|false|none||none|
|» usage|object|true|none||none|
|»» prompt_tokens|integer|true|none||none|
|»» completion_tokens|integer|true|none||none|
|»» total_tokens|integer|true|none||none|

# 薛定猫API官方文档/ideogram 绘画

## POST ideogram（文生图）

POST /ideogram/generate

Generates images synchronously based on a given prompt and optional parameters.
具体参数请看官方文档：https://developer.ideogram.ai/api-reference/generate/post-generate-image

根据给定的提示和可选参数同步生成图像。
返回的图像 URL 在 24 小时内有效，超过该时间将无法访问图像。
已反代图片

> Body 请求参数

```json
{
  "image_request": {
    "aspect_ratio": "ASPECT_10_16",
    "magic_prompt_option": "AUTO",
    "model": "V_1",
    "prompt": "A serene tropical beach scene. Dominating the foreground are tall palm trees with lush green leaves, standing tall against a backdrop of a sandy beach. The beach leads to the azure waters of the sea, which gently kisses the shoreline. In the distance, there is an island or landmass with a silhouette of what appears to be a lighthouse or tower. The sky above is painted with fluffy white clouds, some of which are tinged with hues of pink and orange, suggesting either a sunrise or sunset."
  }
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Content-Type|header|string| 是 |none|
|Accept|header|string| 是 |none|
|Authorization|header|string| 是 |none|
|body|body|object| 否 |none|
|» image_request|body|object| 是 |图像请求对象 (必填)|
|»» prompt|body|string| 是 |用于生成图像的提示词 (必填)|
|»» aspect_ratio|body|string| 是 |图像宽高比 (可选) 可选值:ASPECT_10_16/ASPECT_16_10/ASPECT_9_16/ASPECT_16_9/ASPECT_3_2/ASPECT_2_3/ASPECT_4_3/ASPECT_3_4/ASPECT_1_1/ASPECT_1_3/ASPECT_3_1|
|»» model|body|string| 是 |使用的模型 (可选) 默认V_2,可选值:V_1/V_1_TURBO/V_2/V_2_TURBO|
|»» magic_prompt_option|body|string| 是 |是否使用MagicPrompt (可选) 可选值:AUTO/ON/OFF|
|»» seed|body|integer| 是 |随机种子 (可选) 范围:0-2147483647|
|»» style_type|body|string| 是 |风格类型 (可选) 可选值:AUTO/GENERAL/REALISTIC/DESIGN/RENDER_3D/ANIME|
|»» negative_prompt|body|string| 是 |反向提示词 (可选) 描述不想在图像中出现的内容|
|»» num_images|body|integer| 是 |生成图片数量 (可选) 范围:1-8,默认1|
|»» resolution|body|string| 是 |分辨率 (可选) 可选值包含从512x1536到1536x640等多种分辨率组合|
|»» color_palette|body|object| 是 |颜色调色板 (可选)|
|»»» name|body|string| 是 |预设调色板名称 (与members二选一) 可选值:EMBER/FRESH/JUNGLE/MAGIC/MELON/MOSAIC/PASTEL/ULTRAMARINE|

> 返回示例

> 200 Response

```json
{
  "created": "2024-12-15T17:32:00.965408+00:00",
  "data": [
    {
      "is_image_safe": true,
      "prompt": "A serene tropical beach scene. Dominating the foreground are tall palm trees with lush green leaves, standing tall against a backdrop of a sandy beach. The beach leads to the azure waters of the sea, which gently kisses the shoreline. In the distance, there is an island or landmass with a silhouette of what appears to be a lighthouse or tower. The sky above is painted with fluffy white clouds, some of which are tinged with hues of pink and orange, suggesting either a sunrise or sunset.",
      "resolution": "768x1232",
      "seed": 1785282233,
      "style_type": null,
      "url": "https://ideogram.ai/api/images/ephemeral/WkoxvqiOTaaCqG1nO2tQoA.png?exp=1734370337&sig=110fe96dc9e01002c8d837e5b4cde1aaa266195561d231ce76e19e095e478ffe"
    }
  ]
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» created|string|true|none||none|
|» data|[object]|true|none||none|
|»» is_image_safe|boolean|false|none||none|
|»» prompt|string|false|none||none|
|»» resolution|string|false|none||none|
|»» seed|integer|false|none||none|
|»» style_type|null|false|none||none|
|»» url|string|false|none||none|

## POST Remix（混合图）

POST /ideogram/remix

> Body 请求参数

```yaml
image_request: "{\r

  \    \"image_weight\": 50,\r

  \    \"model\": \"V_1\",\r

  \    \"magic_prompt_option\": \"AUTO\",\r

  \    \"prompt\": \"A%20serene%20tropical%20beach%20\",\r

  \    \"aspect_ratio\": \"ASPECT_10_16\",\r

  \    \"seed\": 12345,\r

  \    \"negative_prompt\": \"brush%20strokes%2C%20painting\"\r

  }"
image_file: file://E:\zuomian\微信截图_20241130193920.png

```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Content-Type|header|string| 是 |none|
|Accept|header|string| 是 |none|
|Authorization|header|string| 是 |none|
|body|body|object| 否 |none|
|» image_request|body|string| 否 |"prompt": "a beautiful sunset over mountains",  // 用于生成图像的提示词 (必填)|
|» image_file|body|string(binary)| 否 |图片文件 (必填) 用于生成新图像的源图片文件|

#### 详细说明

**» image_request**: "prompt": "a beautiful sunset over mountains",  // 用于生成图像的提示词 (必填)
    "aspect_ratio": "ASPECT_16_9",  // 图像宽高比 (可选) 可选值:ASPECT_10_16/ASPECT_16_10/ASPECT_9_16/ASPECT_16_9/ASPECT_3_2/ASPECT_2_3/ASPECT_4_3/ASPECT_3_4/ASPECT_1_1/ASPECT_1_3/ASPECT_3_1
    "color_palette": {  // 颜色调色板 (可选)
      "name": "FRESH"  // 预设调色板名称 (与members二选一) 可选值:EMBER/FRESH/JUNGLE/MAGIC/MELON/MOSAIC/PASTEL/ULTRAMARINE
      // 或者使用自定义颜色:
      /*"members": [
        {
          "color_hex": "#FF0000",  // 颜色十六进制值 (必填) 
          "color_weight": 1.0  // 颜色权重 (可选) 范围:0.05-1.0
        }
      ]*/
    },
    "image_weight": 50,  // 图像权重 (可选) 范围:1-100,默认50
    "magic_prompt_option": "AUTO",  // 是否使用MagicPrompt (可选) 可选值:AUTO/ON/OFF
    "model": "V_2",  // 使用的模型 (可选) 默认V_2,可选值:V_1/V_1_TURBO/V_2/V_2_TURBO
    "negative_prompt": "clouds,blur",  // 反向提示词 (可选) 描述不想在图像中出现的内容
    "num_images": 1,  // 生成图片数量 (可选) 范围:1-8,默认1
    "resolution": "RESOLUTION_1024_1024",  // 分辨率 (可选) 可选值包含从512x1536到1536x640等多种分辨率组合
    "seed": 123456,  // 随机种子 (可选) 范围:0-2147483647
    "style_type": "REALISTIC"  // 风格类型 (可选) 可选值:AUTO/GENERAL/REALISTIC/DESIGN/RENDER_3D/ANIME

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "message": "SUCCEED",
  "request_id": "CjMT7WdSwWcAAAAAALvB3g",
  "data": {
    "task_id": "CjMT7WdSwWcAAAAAALvB3g",
    "task_status": "submitted",
    "created_at": 1733851336696,
    "updated_at": 1733851336696
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none||none|
|» message|string|true|none||none|
|» request_id|string|true|none||none|
|» data|object|true|none||none|
|»» task_id|string|true|none||none|
|»» task_status|string|true|none||none|
|»» created_at|integer|true|none||none|
|»» updated_at|integer|true|none||none|

## POST Upscale（放大高清）

POST /ideogram/upscale

> Body 请求参数

```yaml
image_request: '{"resemblance":50,"magic_prompt_option":"AUTO","prompt":"A%20serene%20tropical%20beach%20","seed":12345,"detail":50}'
image_file: file://E:\zuomian\微信截图_20241130193920.png

```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Content-Type|header|string| 是 |none|
|Accept|header|string| 是 |none|
|Authorization|header|string| 是 |none|
|body|body|object| 否 |none|
|» image_request|body|string| 否 |"prompt": "a beautiful sunset over mountains",  // 提示词用于指导放大 (可选)|
|» image_file|body|string(binary)| 否 |图片文件 (必填) 需要放大的源图片文件|

#### 详细说明

**» image_request**: "prompt": "a beautiful sunset over mountains",  // 提示词用于指导放大 (可选)
    "resemblance": 50,  // 相似度 (可选) 范围:1-100,默认50
    "detail": 50,  // 细节程度 (可选) 范围:1-100,默认50 
    "magic_prompt_option": "AUTO",  // 是否使用MagicPrompt (可选) 可选值:AUTO/ON/OFF
    "num_images": 1,  // 生成图片数量 (可选) 范围:1-8,默认1
    "seed": 123456  // 随机种子 (可选) 范围:0-2147483647

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "message": "SUCCEED",
  "request_id": "CjMT7WdSwWcAAAAAALvB3g",
  "data": {
    "task_id": "CjMT7WdSwWcAAAAAALvB3g",
    "task_status": "submitted",
    "created_at": 1733851336696,
    "updated_at": 1733851336696
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none||none|
|» message|string|true|none||none|
|» request_id|string|true|none||none|
|» data|object|true|none||none|
|»» task_id|string|true|none||none|
|»» task_status|string|true|none||none|
|»» created_at|integer|true|none||none|
|»» updated_at|integer|true|none||none|

## POST Describe（描述）

POST /ideogram/describe

> Body 请求参数

```yaml
image_file: file://E:\zuomian\微信截图_20241130193920.png

```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Content-Type|header|string| 是 |none|
|Accept|header|string| 是 |none|
|Authorization|header|string| 是 |none|
|body|body|object| 否 |none|
|» image_file|body|string(binary)| 否 |(必填) 源图片文件|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "message": "SUCCEED",
  "request_id": "CjMT7WdSwWcAAAAAALvB3g",
  "data": {
    "task_id": "CjMT7WdSwWcAAAAAALvB3g",
    "task_status": "submitted",
    "created_at": 1733851336696,
    "updated_at": 1733851336696
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none||none|
|» message|string|true|none||none|
|» request_id|string|true|none||none|
|» data|object|true|none||none|
|»» task_id|string|true|none||none|
|»» task_status|string|true|none||none|
|»» created_at|integer|true|none||none|
|»» updated_at|integer|true|none||none|

# 薛定猫API官方文档/文生音乐 Suno

## POST 文生音乐（Chat格式）

POST /v1/chat/completions

给定一个提示，该模型将返回一个或多个预测的完成，并且还可以返回每个位置的替代标记的概率。

为提供的提示和参数创建完成

> Body 请求参数

```json
{
  "model": "suno-v3",
  "messages": [
    {
      "role": "user",
      "content": "写一首动听的情歌,送给我的老婆"
    }
  ],
  "stream": false
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Content-Type|header|string| 是 |none|
|Accept|header|string| 是 |none|
|Authorization|header|string| 否 |none|
|body|body|object| 否 |none|
|» model|body|string| 是 |要使用的模型的 ID。有关哪些模型可与聊天 API 一起使用的详细信息,请参阅模型端点兼容性表。|
|» messages|body|[object]| 是 |至今为止对话所包含的消息列表。Python 代码示例。|
|»» role|body|string| 否 |none|
|»» content|body|string| 否 |none|
|» temperature|body|integer| 否 |使用什么采样温度，介于 0 和 2 之间。较高的值（如 0.8）将使输出更加随机，而较低的值（如 0.2）将使输出更加集中和确定。  我们通常建议改变这个或`top_p`但不是两者。|
|» top_p|body|integer| 否 |一种替代温度采样的方法，称为核采样，其中模型考虑具有 top_p 概率质量的标记的结果。所以 0.1 意味着只考虑构成前 10% 概率质量的标记。  我们通常建议改变这个或`temperature`但不是两者。|
|» n|body|integer| 否 |默认为 1|
|» stream|body|boolean| 否 |默认为 false 如果设置,则像在 ChatGPT 中一样会发送部分消息增量。标记将以仅数据的服务器发送事件的形式发送,这些事件在可用时,并在 data: [DONE] 消息终止流。Python 代码示例。|
|» stop|body|string| 否 |默认为 null 最多 4 个序列,API 将停止进一步生成标记。|
|» max_tokens|body|integer| 否 |默认为 inf|
|» presence_penalty|body|number| 否 |-2.0 和 2.0 之间的数字。正值会根据到目前为止是否出现在文本中来惩罚新标记，从而增加模型谈论新主题的可能性。  [查看有关频率和存在惩罚的更多信息。](https://platform.openai.com/docs/api-reference/parameter-details)|
|» frequency_penalty|body|number| 否 |默认为 0 -2.0 到 2.0 之间的数字。正值根据文本目前的存在频率惩罚新标记,降低模型重复相同行的可能性。  有关频率和存在惩罚的更多信息。|
|» logit_bias|body|null| 否 |修改指定标记出现在补全中的可能性。|
|» user|body|string| 否 |代表您的最终用户的唯一标识符，可以帮助 OpenAI 监控和检测滥用行为。[了解更多](https://platform.openai.com/docs/guides/safety-best-practices/end-user-ids)。|
|» response_format|body|object| 否 |指定模型必须输出的格式的对象。  将 { "type": "json_object" } 启用 JSON 模式,这可以确保模型生成的消息是有效的 JSON。  重要提示:使用 JSON 模式时,还必须通过系统或用户消息指示模型生成 JSON。如果不这样做,模型可能会生成无休止的空白流,直到生成达到令牌限制,从而导致延迟增加和请求“卡住”的外观。另请注意,如果 finish_reason="length",则消息内容可能会被部分切断,这表示生成超过了 max_tokens 或对话超过了最大上下文长度。  显示属性|
|» seen|body|integer| 否 |此功能处于测试阶段。如果指定,我们的系统将尽最大努力确定性地进行采样,以便使用相同的种子和参数进行重复请求应返回相同的结果。不能保证确定性,您应该参考 system_fingerprint 响应参数来监控后端的更改。|
|» tools|body|[string]| 是 |模型可以调用的一组工具列表。目前,只支持作为工具的函数。使用此功能来提供模型可以为之生成 JSON 输入的函数列表。|
|» tool_choice|body|object| 是 |控制模型调用哪个函数(如果有的话)。none 表示模型不会调用函数,而是生成消息。auto 表示模型可以在生成消息和调用函数之间进行选择。通过 {"type": "function", "function": {"name": "my_function"}} 强制模型调用该函数。  如果没有函数存在,默认为 none。如果有函数存在,默认为 auto。  显示可能的类型|

#### 详细说明

**» n**: 默认为 1
为每个输入消息生成多少个聊天补全选择。

**» max_tokens**: 默认为 inf
在聊天补全中生成的最大标记数。

输入标记和生成标记的总长度受模型的上下文长度限制。计算标记的 Python 代码示例。

**» logit_bias**: 修改指定标记出现在补全中的可能性。

接受一个 JSON 对象,该对象将标记(由标记器指定的标记 ID)映射到相关的偏差值(-100 到 100)。从数学上讲,偏差在对模型进行采样之前添加到模型生成的 logit 中。确切效果因模型而异,但-1 和 1 之间的值应减少或增加相关标记的选择可能性;如-100 或 100 这样的值应导致相关标记的禁用或独占选择。

> 返回示例

> 200 Response

```json
{
  "id": "chatcmpl-123",
  "object": "chat.completion",
  "created": 1677652288,
  "choices": [
    {
      "index": 0,
      "message": {
        "role": "assistant",
        "content": "\n\nHello there, how may I assist you today?"
      },
      "finish_reason": "stop"
    }
  ],
  "usage": {
    "prompt_tokens": 9,
    "completion_tokens": 12,
    "total_tokens": 21
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» id|string|true|none||none|
|» object|string|true|none||none|
|» created|integer|true|none||none|
|» choices|[object]|true|none||none|
|»» index|integer|false|none||none|
|»» message|object|false|none||none|
|»»» role|string|true|none||none|
|»»» content|string|true|none||none|
|»» finish_reason|string|false|none||none|
|» usage|object|true|none||none|
|»» prompt_tokens|integer|true|none||none|
|»» completion_tokens|integer|true|none||none|
|»» total_tokens|integer|true|none||none|

# 薛定猫API官方文档/文生音乐 Suno/任务提交

## POST 生成歌曲(灵感、自定义、续写)

POST /suno/submit/music

## 任务查询与模式说明

任务创建后，需要通过 task id 轮询查询接口获取任务进度和结果。以下是三种不同的创作模式：

### 1. 灵感模式

最简单的创作模式，只需提供基本描述即可自动生成完整歌曲。

```json
{
    "gpt_description_prompt": "创作描述",
    "make_instrumental": true/false,
    "mv": "chirp-v3-5"
}
```

**特点：**
- Suno自动生成歌词
- 自动生成标题
- 自动生成风格标签
- 最简化的参数要求

### 2. 自定义模式

允许用户对创作进行更多控制的模式。

```json
{
    "prompt": "创作提示词",
    "title": "歌曲标题",
    "tags": "风格1,风格2,风格3",
    "make_instrumental": true/false,
    "mv": "chirp-v3-5"
}
```

**特点：**
- 可以指定具体的创作提示
- 自定义歌曲标题
- 自定义风格标签
- 更精确的创作控制

### 3. 续写模式

在自定义模式基础上进行歌曲续写。

#### 普通续写
```json
{
    "prompt": "创作提示词",
    "title": "歌曲标题",
    "tags": "风格1,风格2,风格3",
    "make_instrumental": true/false,
    "mv": "chirp-v3-5",
    "task_id": "原任务ID",
    "continue_at": 120.00,
    "continue_clip_id": "原歌曲ID"
}
```

#### 上传音频续写
```json
{
    "prompt": "创作提示词",
    "title": "歌曲标题",
    "tags": "风格1,风格2,风格3",
    "make_instrumental": true/false,
    "mv": "chirp-v3-5-upload",
    "task": "extend",
    "task_id": "原任务ID",
    "continue_at": 120.00,
    "continue_clip_id": "原歌曲ID"
}
```

**特点：**
- 支持在现有歌曲基础上继续创作
- 可以指定续写的起始时间点
- 支持上传自定义音频续写
- 保持风格的连贯性

> Body 请求参数

```json
{
  "prompt": "string",
  "mv": "string",
  "title": "string",
  "tags": "string",
  "make_instrumental": true,
  "task_id": "string",
  "continue_at": 0,
  "continue_clip_id": "string",
  "gpt_description_prompt": "string",
  "notify_hook": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Content-Type|header|string| 是 |none|
|Accept|header|string| 是 |none|
|Authorization|header|string| 否 |none|
|body|body|object| 否 |none|
|» prompt|body|string| 是 |歌词内容，仅用于自定义模式|
|» mv|body|string| 是 |模型选择，可选 chirp-v3-0、chirp-v3-5，默认为 chirp-v3-0|
|» title|body|string| 是 |歌曲标题，仅用于自定义模式|
|» tags|body|string| 是 |风格标签，仅用于自定义模式，多个标签用半角逗号分隔|
|» make_instrumental|body|boolean| 是 |是否生成纯音乐版本，true 表示生成纯音乐|
|» task_id|body|string| 是 |任务ID，用于对已有任务进行操作（如续写）|
|» continue_at|body|number| 是 |续写起始时间点，浮点数，单位为秒|
|» continue_clip_id|body|string| 是 |需要续写的歌曲ID|
|» gpt_description_prompt|body|string| 是 |创作描述提示词，仅用于灵感模式|
|» notify_hook|body|string| 是 |任务完成后的回调通知地址|

> 返回示例

> 200 Response

```json
{
  "code": "success",
  "data": "950bf3af-78a6-420e-8c01-3bde0bbb3ef9",
  "message": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|string|true|none||none|
|» data|string|true|none||none|
|» message|string|true|none||none|

## POST 生成歌词

POST /suno/submit/lyrics

> Body 请求参数

```json
{
  "prompt": "string",
  "notify_hook": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Content-Type|header|string| 是 |none|
|Accept|header|string| 是 |none|
|Authorization|header|string| 否 |none|
|body|body|object| 否 |none|
|» prompt|body|string| 是 |歌词提示词|
|» notify_hook|body|string| 是 |回调地址|

> 返回示例

> 200 Response

```json
{
  "code": "success",
  "data": "47443cc1-4902-42ae-ae7f-72a9900544e9",
  "message": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|string|true|none||none|
|» data|string|true|none||none|
|» message|string|true|none||none|

## POST 歌曲拼接

POST /suno/submit/concat

> Body 请求参数

```json
{
  "clip_id": "string",
  "is_infill": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Content-Type|header|string| 是 |none|
|Accept|header|string| 是 |none|
|Authorization|header|string| 否 |none|
|body|body|object| 否 |none|
|» clip_id|body|string| 是 |extend 后的 歌曲ID|
|» is_infill|body|string| 是 |none|

> 返回示例

> 200 Response

```json
{
  "code": "success",
  "data": "47443cc1-4902-42ae-ae7f-72a9900544e9",
  "message": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|string|true|none||none|
|» data|string|true|none||none|
|» message|string|true|none||none|

# 薛定猫API官方文档/文生音乐 Suno/查询接口

## POST 批量获取任务

POST /suno/fetch

> Body 请求参数

```json
{
  "clip_id": "string",
  "is_infill": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Content-Type|header|string| 是 |none|
|Accept|header|string| 是 |none|
|Authorization|header|string| 否 |none|
|body|body|object| 否 |none|
|» clip_id|body|string| 是 |extend 后的 歌曲ID|
|» is_infill|body|string| 是 |none|

> 返回示例

> 200 Response

```json
{
  "code": "success",
  "data": "47443cc1-4902-42ae-ae7f-72a9900544e9",
  "message": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|string|true|none||none|
|» data|string|true|none||none|
|» message|string|true|none||none|

## GET 查询单个任务

GET /suno/fetch/{task_id}

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|task_id|path|string| 是 |none|
|Content-Type|header|string| 是 |none|
|Accept|header|string| 是 |none|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": "success",
  "data": "47443cc1-4902-42ae-ae7f-72a9900544e9",
  "message": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|string|true|none||none|
|» data|string|true|none||none|
|» message|string|true|none||none|

# 数据模型

<h2 id="tocS_Pet">Pet</h2>

<a id="schemapet"></a>
<a id="schema_Pet"></a>
<a id="tocSpet"></a>
<a id="tocspet"></a>

```json
{
  "id": 1,
  "category": {
    "id": 1,
    "name": "string"
  },
  "name": "doggie",
  "photoUrls": [
    "string"
  ],
  "tags": [
    {
      "id": 1,
      "name": "string"
    }
  ],
  "status": "available"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|true|none||宠物ID编号|
|category|[Category](#schemacategory)|true|none||分组|
|name|string|true|none||名称|
|photoUrls|[string]|true|none||照片URL|
|tags|[[Tag](#schematag)]|true|none||标签|
|status|string|true|none||宠物销售状态|

#### 枚举值

|属性|值|
|---|---|
|status|available|
|status|pending|
|status|sold|

<h2 id="tocS_Category">Category</h2>

<a id="schemacategory"></a>
<a id="schema_Category"></a>
<a id="tocScategory"></a>
<a id="tocscategory"></a>

```json
{
  "id": 1,
  "name": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||分组ID编号|
|name|string|false|none||分组名称|

<h2 id="tocS_Tag">Tag</h2>

<a id="schematag"></a>
<a id="schema_Tag"></a>
<a id="tocStag"></a>
<a id="tocstag"></a>

```json
{
  "id": 1,
  "name": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||标签ID编号|
|name|string|false|none||标签名称|

