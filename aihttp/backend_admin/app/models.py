# -*- coding: utf-8 -*-
"""数据库模型"""
import json
from datetime import datetime
from werkzeug.security import generate_password_hash, check_password_hash
from . import db

class User(db.Model):
    """用户模型"""
    __tablename__ = 'users'

    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(64), unique=True, index=True, nullable=False)
    email = db.Column(db.String(120), unique=True, index=True, nullable=False)
    password_hash = db.Column(db.String(128))
    is_admin = db.Column(db.<PERSON>, default=False)
    is_paid_member = db.Column(db.<PERSON>, default=False)
    membership_expiry = db.Column(db.DateTime)
    created_at = db.Column(db.DateTime, default=datetime.now)
    last_login = db.Column(db.DateTime)

    def set_password(self, password):
        """设置密码"""
        self.password_hash = generate_password_hash(password)

    def check_password(self, password):
        """验证密码"""
        return check_password_hash(self.password_hash, password)

    def is_membership_valid(self):
        """检查会员是否有效"""
        if not self.is_paid_member:
            return False
        if not self.membership_expiry:
            return False
        return self.membership_expiry > datetime.now()

    def to_dict(self):
        """转换为字典"""
        return {
            "id": self.id,
            "username": self.username,
            "email": self.email,
            "is_admin": self.is_admin,
            "is_paid_member": self.is_paid_member,
            "membership_valid": self.is_membership_valid(),
            "membership_expiry": self.membership_expiry.strftime("%Y-%m-%d %H:%M:%S") if self.membership_expiry else None,
            "created_at": self.created_at.strftime("%Y-%m-%d %H:%M:%S")
        }

class MembershipPlan(db.Model):
    """会员计划模型"""
    __tablename__ = 'membership_plans'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(64), nullable=False)
    description = db.Column(db.Text)
    price = db.Column(db.Float, nullable=False)
    duration_months = db.Column(db.Integer, nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.now)
    is_active = db.Column(db.Boolean, default=True)

    def to_dict(self):
        """转换为字典"""
        return {
            "id": self.id,
            "name": self.name,
            "description": self.description,
            "price": self.price,
            "duration_months": self.duration_months,
            "is_active": self.is_active,
            "created_at": self.created_at.strftime("%Y-%m-%d %H:%M:%S")
        }

class Order(db.Model):
    """订单模型"""
    __tablename__ = 'orders'

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    plan_id = db.Column(db.Integer, db.ForeignKey('membership_plans.id'), nullable=False)
    amount = db.Column(db.Float, nullable=False)
    status = db.Column(db.String(20), default='pending')  # pending, completed, failed
    created_at = db.Column(db.DateTime, default=datetime.now)
    paid_at = db.Column(db.DateTime)

    # 关系
    user = db.relationship('User', backref=db.backref('orders', lazy='dynamic'))
    plan = db.relationship('MembershipPlan', backref=db.backref('orders', lazy='dynamic'))

    def to_dict(self):
        """转换为字典"""
        return {
            "id": self.id,
            "user_id": self.user_id,
            "plan_id": self.plan_id,
            "amount": self.amount,
            "status": self.status,
            "created_at": self.created_at.strftime("%Y-%m-%d %H:%M:%S"),
            "paid_at": self.paid_at.strftime("%Y-%m-%d %H:%M:%S") if self.paid_at else None,
            "plan": self.plan.to_dict() if self.plan else None,
            "user": {"id": self.user.id, "username": self.user.username} if self.user else None
        }

class Software(db.Model):
    """软件模型 - 扩展为API服务集成平台"""
    __tablename__ = 'software'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(64), nullable=False)
    description = db.Column(db.Text)
    software_type = db.Column(db.String(20), default='download')  # download, api_service, hybrid
    category = db.Column(db.String(50))  # frontend, desktop, mobile, api_tool, ai_service

    # API服务配置
    api_config = db.Column(db.Text)  # JSON格式存储API配置
    auth_required = db.Column(db.Boolean, default=True)  # 是否需要认证
    membership_required = db.Column(db.Boolean, default=False)  # 是否需要会员

    # 使用统计
    download_count = db.Column(db.Integer, default=0)
    api_call_count = db.Column(db.Integer, default=0)

    created_at = db.Column(db.DateTime, default=datetime.now)
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)
    is_active = db.Column(db.Boolean, default=True)

    # 关系
    versions = db.relationship('SoftwareVersion', backref='software', lazy='dynamic', cascade='all, delete-orphan')
    api_endpoints = db.relationship('ApiEndpoint', backref='software', lazy='dynamic', cascade='all, delete-orphan')

    def get_api_config(self):
        """获取API配置"""
        if self.api_config:
            try:
                return json.loads(self.api_config)
            except:
                return {}
        return {}

    def set_api_config(self, config):
        """设置API配置"""
        self.api_config = json.dumps(config)

    def to_dict(self):
        """转换为字典"""
        latest_version = self.versions.order_by(SoftwareVersion.version_number.desc()).first()
        return {
            "id": self.id,
            "name": self.name,
            "description": self.description,
            "software_type": self.software_type,
            "category": self.category,
            "auth_required": self.auth_required,
            "membership_required": self.membership_required,
            "download_count": self.download_count,
            "api_call_count": self.api_call_count,
            "is_active": self.is_active,
            "created_at": self.created_at.strftime("%Y-%m-%d %H:%M:%S"),
            "updated_at": self.updated_at.strftime("%Y-%m-%d %H:%M:%S"),
            "latest_version": latest_version.to_dict() if latest_version else None,
            "api_config": self.get_api_config(),
            "api_endpoints": []  # 临时移除关系查询
        }

class SoftwareVersion(db.Model):
    """软件版本模型"""
    __tablename__ = 'software_versions'

    id = db.Column(db.Integer, primary_key=True)
    software_id = db.Column(db.Integer, db.ForeignKey('software.id'), nullable=False)
    version_number = db.Column(db.String(20), nullable=False)
    release_notes = db.Column(db.Text)
    file_path = db.Column(db.String(255), nullable=True)  # 允许为空，用于本地上传的文件
    external_url = db.Column(db.String(255), nullable=True)  # 新增：外部URL
    is_external = db.Column(db.Boolean, default=False)  # 新增：是否使用外部URL
    created_at = db.Column(db.DateTime, default=datetime.now)
    is_active = db.Column(db.Boolean, default=True)

    def to_dict(self):
        """转换为字典"""
        return {
            "id": self.id,
            "software_id": self.software_id,
            "version_number": self.version_number,
            "release_notes": self.release_notes,
            "is_external": self.is_external,
            "external_url": self.external_url if self.is_external else None,
            "created_at": self.created_at.strftime("%Y-%m-%d %H:%M:%S"),
            "is_active": self.is_active
        }

class MidjourneyTask(db.Model):
    """Midjourney任务模型"""
    __tablename__ = 'midjourney_tasks'

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    task_id = db.Column(db.String(64), nullable=False)  # Midjourney API任务ID
    prompt = db.Column(db.Text, nullable=False)
    status = db.Column(db.String(20), default='pending')  # pending, completed, failed
    result_url = db.Column(db.String(255))  # 结果图片URL
    error_message = db.Column(db.Text)  # 错误信息
    raw_response = db.Column(db.Text)  # 原始API响应
    created_at = db.Column(db.DateTime, default=datetime.now)
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)

    # 关系
    user = db.relationship('User', backref=db.backref('midjourney_tasks', lazy='dynamic'))

    def to_dict(self):
        """转换为字典"""
        data = {
            "id": self.id,
            "user_id": self.user_id,
            "task_id": self.task_id,
            "prompt": self.prompt,
            "status": self.status,
            "result_url": self.result_url,
            "error_message": self.error_message,
            "created_at": self.created_at.strftime("%Y-%m-%d %H:%M:%S"),
            "updated_at": self.updated_at.strftime("%Y-%m-%d %H:%M:%S")
        }

        # 如果有原始响应，解析并添加到结果中
        if self.raw_response:
            try:
                raw_data = json.loads(self.raw_response)
                data["api_response"] = raw_data
            except:
                pass  # 如果无法解析JSON，忽略

        return data

class ApiEndpoint(db.Model):
    """API端点模型 - 管理软件可调用的API接口"""
    __tablename__ = 'api_endpoints'

    id = db.Column(db.Integer, primary_key=True)
    software_id = db.Column(db.Integer, db.ForeignKey('software.id'), nullable=False)
    name = db.Column(db.String(64), nullable=False)  # 端点名称
    description = db.Column(db.Text)  # 端点描述
    endpoint_type = db.Column(db.String(20), nullable=False)  # ai_service, proxy, custom, llm

    # 端点配置
    target_url = db.Column(db.String(255))  # 目标API URL
    method = db.Column(db.String(10), default='POST')  # HTTP方法
    headers_config = db.Column(db.Text)  # 请求头配置 (JSON)
    params_config = db.Column(db.Text)  # 参数配置 (JSON)
    auth_config = db.Column(db.Text)  # 认证配置 (JSON)

    # 权限控制
    auth_required = db.Column(db.Boolean, default=True)
    membership_required = db.Column(db.Boolean, default=False)
    rate_limit = db.Column(db.Integer, default=100)  # 每小时调用限制

    # 统计信息
    call_count = db.Column(db.Integer, default=0)
    success_count = db.Column(db.Integer, default=0)
    error_count = db.Column(db.Integer, default=0)

    created_at = db.Column(db.DateTime, default=datetime.now)
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)
    is_active = db.Column(db.Boolean, default=True)

    def get_headers_config(self):
        """获取请求头配置"""
        if self.headers_config:
            try:
                return json.loads(self.headers_config)
            except:
                return {}
        return {}

    def get_params_config(self):
        """获取参数配置"""
        if self.params_config:
            try:
                return json.loads(self.params_config)
            except:
                return {}
        return {}

    def get_auth_config(self):
        """获取认证配置"""
        if self.auth_config:
            try:
                return json.loads(self.auth_config)
            except:
                return {}
        return {}

    def to_dict(self):
        """转换为字典"""
        return {
            "id": self.id,
            "software_id": self.software_id,
            "name": self.name,
            "description": self.description,
            "endpoint_type": self.endpoint_type,
            "target_url": self.target_url,
            "method": self.method,
            "auth_required": self.auth_required,
            "membership_required": self.membership_required,
            "rate_limit": self.rate_limit,
            "call_count": self.call_count,
            "success_count": self.success_count,
            "error_count": self.error_count,
            "success_rate": round(self.success_count / max(self.call_count, 1) * 100, 2),
            "headers_config": self.get_headers_config(),
            "params_config": self.get_params_config(),
            "auth_config": self.get_auth_config(),
            "created_at": self.created_at.strftime("%Y-%m-%d %H:%M:%S"),
            "updated_at": self.updated_at.strftime("%Y-%m-%d %H:%M:%S"),
            "is_active": self.is_active
        }

class ApiCallLog(db.Model):
    """API调用日志模型"""
    __tablename__ = 'api_call_logs'

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    software_id = db.Column(db.Integer, db.ForeignKey('software.id'), nullable=False)
    endpoint_id = db.Column(db.Integer, db.ForeignKey('api_endpoints.id'), nullable=False)

    # 请求信息
    request_method = db.Column(db.String(10))
    request_url = db.Column(db.String(255))
    request_headers = db.Column(db.Text)
    request_body = db.Column(db.Text)

    # 响应信息
    response_status = db.Column(db.Integer)
    response_headers = db.Column(db.Text)
    response_body = db.Column(db.Text)
    response_time = db.Column(db.Float)  # 响应时间(秒)

    # 状态
    status = db.Column(db.String(20), default='success')  # success, error, timeout
    error_message = db.Column(db.Text)

    created_at = db.Column(db.DateTime, default=datetime.now)

    # 关系
    user = db.relationship('User', backref=db.backref('api_call_logs', lazy='dynamic'))
    software = db.relationship('Software', backref=db.backref('api_call_logs', lazy='dynamic'))
    endpoint = db.relationship('ApiEndpoint', backref=db.backref('api_call_logs', lazy='dynamic'))

    def to_dict(self):
        """转换为字典"""
        return {
            "id": self.id,
            "user_id": self.user_id,
            "software_id": self.software_id,
            "endpoint_id": self.endpoint_id,
            "request_method": self.request_method,
            "request_url": self.request_url,
            "response_status": self.response_status,
            "response_time": self.response_time,
            "status": self.status,
            "error_message": self.error_message,
            "created_at": self.created_at.strftime("%Y-%m-%d %H:%M:%S"),
            "user": {"id": self.user.id, "username": self.user.username} if self.user else None,
            "software": {"id": self.software.id, "name": self.software.name} if self.software else None,
            "endpoint": {"id": self.endpoint.id, "name": self.endpoint.name} if self.endpoint else None
        }
