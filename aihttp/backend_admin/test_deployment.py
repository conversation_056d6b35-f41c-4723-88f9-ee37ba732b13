#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
部署测试脚本 - 验证新服务器部署是否成功
"""
import requests
import json
import time

# 新服务器配置
NEW_IP = "************"
NEW_DOMAIN = "ai.http28.com"
BASE_URL = f"http://{NEW_IP}:5000"

def test_basic_access():
    """测试基本访问"""
    print("🔍 测试基本访问...")
    
    try:
        # 测试主页
        response = requests.get(BASE_URL, timeout=10)
        if response.status_code == 200:
            print("✅ 主页访问正常")
        else:
            print(f"❌ 主页访问失败，状态码: {response.status_code}")
            return False
        
        # 测试管理后台
        response = requests.get(f"{BASE_URL}/admin/", timeout=10)
        if response.status_code == 200:
            print("✅ 管理后台访问正常")
        else:
            print(f"❌ 管理后台访问失败，状态码: {response.status_code}")
            return False
        
        return True
    except Exception as e:
        print(f"❌ 基本访问测试失败: {e}")
        return False

def test_api_endpoints():
    """测试API接口"""
    print("\n🔍 测试API接口...")
    
    try:
        # 测试用户注册
        register_data = {
            "username": f"test_user_{int(time.time())}",
            "email": f"test_{int(time.time())}@example.com",
            "password": "test123456"
        }
        
        response = requests.post(f"{BASE_URL}/api/auth/register", 
                               json=register_data, timeout=10)
        if response.status_code == 201:
            print("✅ 用户注册API正常")
            user_data = response.json()
            access_token = user_data.get('access_token')
        else:
            print(f"❌ 用户注册API失败，状态码: {response.status_code}")
            return False
        
        # 测试用户登录
        login_data = {
            "username": register_data["username"],
            "password": register_data["password"]
        }
        
        response = requests.post(f"{BASE_URL}/api/auth/login", 
                               json=login_data, timeout=10)
        if response.status_code == 200:
            print("✅ 用户登录API正常")
        else:
            print(f"❌ 用户登录API失败，状态码: {response.status_code}")
            return False
        
        # 测试获取用户信息
        headers = {"Authorization": f"Bearer {access_token}"}
        response = requests.get(f"{BASE_URL}/api/auth/profile", 
                              headers=headers, timeout=10)
        if response.status_code == 200:
            print("✅ 获取用户信息API正常")
        else:
            print(f"❌ 获取用户信息API失败，状态码: {response.status_code}")
            return False
        
        # 测试软件列表API
        response = requests.get(f"{BASE_URL}/api/software/list", 
                              headers=headers, timeout=10)
        if response.status_code == 200:
            print("✅ 软件列表API正常")
        else:
            print(f"❌ 软件列表API失败，状态码: {response.status_code}")
            return False
        
        return True
    except Exception as e:
        print(f"❌ API接口测试失败: {e}")
        return False

def test_admin_login():
    """测试管理员登录"""
    print("\n🔍 测试管理员登录...")
    
    try:
        # 管理员登录
        admin_data = {
            "username": "admin",
            "password": "admin123"
        }
        
        response = requests.post(f"{BASE_URL}/api/auth/login", 
                               json=admin_data, timeout=10)
        if response.status_code == 200:
            result = response.json()
            if result.get('user', {}).get('is_admin'):
                print("✅ 管理员登录正常")
                return True
            else:
                print("❌ 登录用户不是管理员")
                return False
        else:
            print(f"❌ 管理员登录失败，状态码: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 管理员登录测试失败: {e}")
        return False

def test_server_performance():
    """测试服务器性能"""
    print("\n🔍 测试服务器性能...")
    
    try:
        # 测试响应时间
        start_time = time.time()
        response = requests.get(BASE_URL, timeout=10)
        end_time = time.time()
        
        response_time = (end_time - start_time) * 1000  # 转换为毫秒
        
        if response_time < 1000:  # 小于1秒
            print(f"✅ 响应时间正常: {response_time:.2f}ms")
        else:
            print(f"⚠️ 响应时间较慢: {response_time:.2f}ms")
        
        # 测试并发请求
        print("测试并发请求...")
        import concurrent.futures
        
        def make_request():
            try:
                response = requests.get(BASE_URL, timeout=5)
                return response.status_code == 200
            except:
                return False
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=5) as executor:
            futures = [executor.submit(make_request) for _ in range(10)]
            results = [future.result() for future in concurrent.futures.as_completed(futures)]
        
        success_count = sum(results)
        if success_count >= 8:  # 80%成功率
            print(f"✅ 并发测试通过: {success_count}/10 成功")
        else:
            print(f"⚠️ 并发测试部分失败: {success_count}/10 成功")
        
        return True
    except Exception as e:
        print(f"❌ 性能测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始部署验证测试")
    print("=" * 60)
    print(f"测试目标: {BASE_URL}")
    print(f"域名: {NEW_DOMAIN}")
    print("=" * 60)
    
    tests = [
        ("基本访问测试", test_basic_access),
        ("API接口测试", test_api_endpoints),
        ("管理员登录测试", test_admin_login),
        ("服务器性能测试", test_server_performance)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}执行失败: {e}")
            results.append((test_name, False))
    
    # 输出测试结果
    print("\n" + "=" * 60)
    print("📋 测试结果总结:")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 总体结果: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 部署验证成功！系统运行正常")
        print("\n🌐 访问信息:")
        print(f"  主页: http://{NEW_IP}:5000")
        print(f"  管理后台: http://{NEW_IP}:5000/admin")
        print(f"  域名访问: http://{NEW_DOMAIN} (需要DNS解析)")
        print(f"  默认管理员: admin / admin123")
    else:
        print("⚠️ 部署验证发现问题，请检查失败的测试项")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
