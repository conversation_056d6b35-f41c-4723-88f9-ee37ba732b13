# 服务器迁移总结 - ai.http28.com

## 🎯 迁移信息

### 新服务器配置
- **域名**: ai.http28.com
- **IP地址**: ************
- **项目路径**: /www/wwwroot/ai.http28.com/backend_admin
- **日志路径**: /www/wwwlogs/python/ai_http28

### 旧服务器配置
- **IP地址**: ************** (已更新)
- **项目路径**: /www/wwwroot/mohcdn/backend_admin (已更新)
- **日志路径**: /www/wwwlogs/python/aimohcdn (已更新)

## ✅ 已完成的配置更新

### 1. Gunicorn配置 (gunicorn_conf.py)
```python
# 更新内容:
chdir = '/www/wwwroot/ai.http28.com/backend_admin'
bind = '************:5000'
pidfile = '/www/wwwroot/ai.http28.com/backend_admin/gunicorn.pid'
accesslog = '/www/wwwlogs/python/ai_http28/gunicorn_acess.log'
errorlog = '/www/wwwlogs/python/ai_http28/gunicorn_error.log'
```

### 2. uWSGI配置 (uwsgi.ini)
```ini
# 更新内容:
chdir=/www/wwwroot/ai.http28.com/backend_admin
wsgi-file=/www/wwwroot/ai.http28.com/backend_admin/start_baota.py
pidfile=/www/wwwroot/ai.http28.com/backend_admin/uwsgi.pid
daemonize = /www/wwwlogs/python/ai_http28/uwsgi.log
```

### 3. Nginx配置 (nginx_ai_http28.conf)
```nginx
# 新增配置文件，包含:
server_name ai.http28.com www.ai.http28.com;
alias /www/wwwroot/ai.http28.com/backend_admin/app/static;
alias /www/wwwroot/ai.http28.com/backend_admin/app/uploads;
```

### 4. 文档更新
- ✅ BAOTA_SETUP.md - 更新路径和域名
- ✅ DEPLOYMENT.md - 更新Nginx配置示例
- ✅ deploy.py - 更新Nginx配置模板
- ✅ check_system.py - 更新访问地址显示

## 🚀 部署步骤

### 方法一：使用自动化脚本
```bash
# 1. 运行迁移检查脚本
chmod +x migrate_server.sh
./migrate_server.sh

# 2. 运行自动部署脚本
sudo python3 deploy_new_server.py
```

### 方法二：手动部署
```bash
# 1. 创建目录结构
sudo mkdir -p /www/wwwroot/ai.http28.com/backend_admin
sudo mkdir -p /www/wwwlogs/python/ai_http28

# 2. 复制项目文件
sudo cp -r . /www/wwwroot/ai.http28.com/backend_admin/
sudo chown -R www:www /www/wwwroot/ai.http28.com/backend_admin

# 3. 配置Nginx
sudo cp nginx_ai_http28.conf /etc/nginx/sites-available/ai_http28
sudo ln -sf /etc/nginx/sites-available/ai_http28 /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx

# 4. 安装依赖并启动
cd /www/wwwroot/ai.http28.com/backend_admin
python3 -m pip install -r requirements.txt
python3 create_admin.py  # 如果需要初始化数据库
gunicorn -c gunicorn_conf.py run:app
```

## 🔧 验证步骤

### 1. 检查服务状态
```bash
# 检查端口监听
netstat -tlnp | grep :5000

# 检查进程
ps aux | grep gunicorn

# 检查日志
tail -f /www/wwwroot/ai.http28.com/backend_admin/logs/backend_admin.log
```

### 2. 测试访问
```bash
# 本地测试
curl http://127.0.0.1:5000

# IP访问测试
curl http://************:5000

# 域名访问测试 (需要DNS解析)
curl http://ai.http28.com
```

### 3. 功能测试
- 🌐 访问主页: http://ai.http28.com
- 🔐 访问管理后台: http://ai.http28.com/admin
- 📱 测试API接口: python3 test_api.py
- 👤 管理员登录: admin / admin123

## 📋 迁移检查清单

### 部署前检查
- [ ] 域名DNS已指向新IP (************)
- [ ] 服务器防火墙已配置 (端口5000, 80, 443)
- [ ] 备份原服务器数据
- [ ] 确认新服务器环境 (Python, Nginx等)

### 部署后检查
- [ ] 服务正常启动
- [ ] 端口正常监听
- [ ] 域名访问正常
- [ ] 管理后台可登录
- [ ] API接口正常响应
- [ ] 文件上传功能正常
- [ ] 数据库数据完整

### 安全检查
- [ ] 修改默认管理员密码
- [ ] 配置SSL证书 (可选)
- [ ] 检查文件权限
- [ ] 配置日志轮转

## 🔒 安全建议

### 1. SSL证书配置
```bash
# 使用Let's Encrypt
sudo apt install certbot python3-certbot-nginx
sudo certbot --nginx -d ai.http28.com
```

### 2. 防火墙配置
```bash
# Ubuntu/Debian
sudo ufw allow 22
sudo ufw allow 80
sudo ufw allow 443
sudo ufw allow 5000
sudo ufw enable
```

### 3. 定期备份
```bash
# 数据库备份
cp /www/wwwroot/ai.http28.com/backend_admin/data/data.sqlite \
   /www/backup/xhs_backend_$(date +%Y%m%d_%H%M%S).sqlite

# 完整备份
tar -czf /www/backup/xhs_backend_$(date +%Y%m%d_%H%M%S).tar.gz \
    /www/wwwroot/ai.http28.com/backend_admin
```

## 📞 故障排除

### 常见问题
1. **端口被占用**: `sudo lsof -i :5000`
2. **权限问题**: `sudo chown -R www:www /www/wwwroot/ai.http28.com/`
3. **Nginx配置错误**: `sudo nginx -t`
4. **Python依赖问题**: `pip install -r requirements.txt`

### 日志查看
```bash
# 应用日志
tail -f /www/wwwroot/ai.http28.com/backend_admin/logs/backend_admin.log

# Nginx日志
tail -f /var/log/nginx/ai_http28_error.log

# Gunicorn日志
tail -f /www/wwwlogs/python/ai_http28/gunicorn_error.log
```

## 🎉 迁移完成

配置文件已全部更新完成，可以按照上述步骤进行服务器迁移。

**重要提醒**: 
- 确保域名DNS已正确指向新IP
- 在新服务器测试完全正常后再停止旧服务器
- 建议先保留旧服务器一段时间作为备份
