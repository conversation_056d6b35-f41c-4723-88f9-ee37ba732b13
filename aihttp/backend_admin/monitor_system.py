#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统监控脚本 - 监控新服务器运行状态
"""
import os
import subprocess
import requests
import time
from datetime import datetime

# 配置
NEW_IP = "************"
BASE_URL = f"http://{NEW_IP}:5000"
PROJECT_PATH = "/www/wwwroot/ai.http28.com/backend_admin"

def check_process_status():
    """检查进程状态"""
    print("🔍 检查进程状态...")

    try:
        # 检查Gunicorn进程
        result = subprocess.run(['ps', 'aux'], stdout=subprocess.PIPE, stderr=subprocess.PIPE, universal_newlines=True)
        gunicorn_processes = [line for line in result.stdout.split('\n') if 'gunicorn' in line and 'grep' not in line]

        if gunicorn_processes:
            print(f"✅ Gunicorn进程运行正常 ({len(gunicorn_processes)} 个进程)")
            for i, process in enumerate(gunicorn_processes[:3]):  # 只显示前3个
                parts = process.split()
                if len(parts) >= 2:
                    print(f"   进程 {i+1}: PID {parts[1]}")
        else:
            print("❌ Gunicorn进程未运行")
            return False

        return True
    except Exception as e:
        print(f"❌ 进程检查失败: {e}")
        return False

def check_port_status():
    """检查端口状态"""
    print("\n🔍 检查端口状态...")

    try:
        result = subprocess.run(['netstat', '-tlnp'], stdout=subprocess.PIPE, stderr=subprocess.PIPE, universal_newlines=True)
        port_5000 = [line for line in result.stdout.split('\n') if ':5000' in line]

        if port_5000:
            print("✅ 端口5000正在监听")
            for line in port_5000:
                if 'LISTEN' in line:
                    print(f"   {line.strip()}")
        else:
            print("❌ 端口5000未监听")
            return False

        return True
    except Exception as e:
        print(f"❌ 端口检查失败: {e}")
        return False

def check_web_access():
    """检查Web访问"""
    print("\n🔍 检查Web访问...")

    try:
        # 检查主页
        response = requests.get(BASE_URL, timeout=10)
        if response.status_code == 200:
            print("✅ 主页访问正常")
        else:
            print(f"❌ 主页访问失败，状态码: {response.status_code}")
            return False

        # 检查管理后台
        response = requests.get(f"{BASE_URL}/admin/", timeout=10)
        if response.status_code == 200:
            print("✅ 管理后台访问正常")
        else:
            print(f"❌ 管理后台访问失败，状态码: {response.status_code}")
            return False

        # 检查API
        response = requests.get(f"{BASE_URL}/api/auth/profile", timeout=10)
        if response.status_code in [200, 401, 422]:  # 401/422是正常的未认证响应
            print("✅ API接口响应正常")
        else:
            print(f"❌ API接口异常，状态码: {response.status_code}")
            return False

        return True
    except Exception as e:
        print(f"❌ Web访问检查失败: {e}")
        return False

def check_disk_space():
    """检查磁盘空间"""
    print("\n🔍 检查磁盘空间...")

    try:
        result = subprocess.run(['df', '-h', PROJECT_PATH], stdout=subprocess.PIPE, stderr=subprocess.PIPE, universal_newlines=True)
        lines = result.stdout.strip().split('\n')

        if len(lines) >= 2:
            header = lines[0]
            data = lines[1].split()
            if len(data) >= 5:
                used_percent = data[4].replace('%', '')
                print(f"✅ 磁盘使用率: {data[4]} (可用: {data[3]})")

                if int(used_percent) > 90:
                    print("⚠️ 磁盘空间不足")
                    return False
            else:
                print("⚠️ 无法解析磁盘信息")

        return True
    except Exception as e:
        print(f"❌ 磁盘空间检查失败: {e}")
        return False

def check_log_files():
    """检查日志文件"""
    print("\n🔍 检查日志文件...")

    try:
        log_files = [
            f"{PROJECT_PATH}/logs/backend_admin.log",
            f"{PROJECT_PATH}/logs/gunicorn.log",
            "/www/wwwlogs/python/ai_http28/gunicorn_error.log"
        ]

        for log_file in log_files:
            if os.path.exists(log_file):
                size = os.path.getsize(log_file)
                size_mb = size / (1024 * 1024)
                print(f"✅ {os.path.basename(log_file)}: {size_mb:.2f}MB")

                if size_mb > 100:  # 大于100MB
                    print(f"⚠️ 日志文件 {os.path.basename(log_file)} 过大")
            else:
                print(f"⚠️ 日志文件不存在: {os.path.basename(log_file)}")

        return True
    except Exception as e:
        print(f"❌ 日志文件检查失败: {e}")
        return False

def check_database():
    """检查数据库"""
    print("\n🔍 检查数据库...")

    try:
        db_file = f"{PROJECT_PATH}/data/data.sqlite"

        if os.path.exists(db_file):
            size = os.path.getsize(db_file)
            size_kb = size / 1024
            print(f"✅ 数据库文件存在: {size_kb:.2f}KB")

            # 检查文件权限
            if os.access(db_file, os.R_OK | os.W_OK):
                print("✅ 数据库文件权限正常")
            else:
                print("❌ 数据库文件权限不足")
                return False
        else:
            print("❌ 数据库文件不存在")
            return False

        return True
    except Exception as e:
        print(f"❌ 数据库检查失败: {e}")
        return False

def get_system_info():
    """获取系统信息"""
    print("\n📊 系统信息:")

    try:
        # CPU使用率
        result = subprocess.run(['top', '-bn1'], stdout=subprocess.PIPE, stderr=subprocess.PIPE, universal_newlines=True)
        cpu_line = [line for line in result.stdout.split('\n') if 'Cpu(s)' in line]
        if cpu_line:
            print(f"   CPU: {cpu_line[0].strip()}")

        # 内存使用
        result = subprocess.run(['free', '-h'], stdout=subprocess.PIPE, stderr=subprocess.PIPE, universal_newlines=True)
        lines = result.stdout.strip().split('\n')
        if len(lines) >= 2:
            print(f"   内存: {lines[1]}")

        # 系统负载
        result = subprocess.run(['uptime'], stdout=subprocess.PIPE, stderr=subprocess.PIPE, universal_newlines=True)
        print(f"   负载: {result.stdout.strip()}")

    except Exception as e:
        print(f"⚠️ 系统信息获取失败: {e}")

def main():
    """主监控函数"""
    print("🔍 XHS后端管理系统 - 运行状态监控")
    print("=" * 60)
    print(f"监控时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"服务器IP: {NEW_IP}")
    print(f"项目路径: {PROJECT_PATH}")
    print("=" * 60)

    checks = [
        ("进程状态", check_process_status),
        ("端口状态", check_port_status),
        ("Web访问", check_web_access),
        ("磁盘空间", check_disk_space),
        ("日志文件", check_log_files),
        ("数据库", check_database)
    ]

    results = []

    for check_name, check_func in checks:
        try:
            result = check_func()
            results.append((check_name, result))
        except Exception as e:
            print(f"❌ {check_name}检查失败: {e}")
            results.append((check_name, False))

    # 获取系统信息
    get_system_info()

    # 总结
    print("\n" + "=" * 60)
    print("📋 监控结果总结:")

    passed = 0
    total = len(results)

    for check_name, result in results:
        status = "✅ 正常" if result else "❌ 异常"
        print(f"  {check_name}: {status}")
        if result:
            passed += 1

    print(f"\n🎯 系统状态: {passed}/{total} 项检查正常")

    if passed == total:
        print("🎉 系统运行状态良好！")
    elif passed >= total * 0.8:  # 80%以上正常
        print("⚠️ 系统基本正常，但有部分问题需要关注")
    else:
        print("❌ 系统存在严重问题，需要立即处理")

    print("=" * 60)
    print(f"下次检查建议: 30分钟后")
    print("=" * 60)

if __name__ == "__main__":
    main()
