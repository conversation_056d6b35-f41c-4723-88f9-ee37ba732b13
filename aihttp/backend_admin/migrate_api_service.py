#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库迁移脚本 - 添加API服务相关表
"""
import os
import sys
import sqlite3
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def migrate_database():
    """执行数据库迁移"""
    db_path = os.path.join(os.path.dirname(__file__), 'data', 'data.sqlite')

    if not os.path.exists(db_path):
        print("❌ 数据库文件不存在")
        return False

    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        print("🔄 开始数据库迁移...")

        # 1. 更新software表，添加新字段
        print("📝 更新software表...")

        # 检查字段是否已存在
        cursor.execute("PRAGMA table_info(software)")
        columns = [column[1] for column in cursor.fetchall()]

        new_columns = [
            ('software_type', 'VARCHAR(20) DEFAULT "download"'),
            ('category', 'VARCHAR(50)'),
            ('api_config', 'TEXT'),
            ('auth_required', 'BOOLEAN DEFAULT 1'),
            ('membership_required', 'BOOLEAN DEFAULT 0'),
            ('download_count', 'INTEGER DEFAULT 0'),
            ('api_call_count', 'INTEGER DEFAULT 0'),
            ('updated_at', 'DATETIME')
        ]

        for column_name, column_def in new_columns:
            if column_name not in columns:
                cursor.execute(f'ALTER TABLE software ADD COLUMN {column_name} {column_def}')
                print(f"  ✅ 添加字段: {column_name}")
            else:
                print(f"  ⏭️ 字段已存在: {column_name}")

        # 2. 创建api_endpoints表
        print("📝 创建api_endpoints表...")
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS api_endpoints (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                software_id INTEGER NOT NULL,
                name VARCHAR(64) NOT NULL,
                description TEXT,
                endpoint_type VARCHAR(20) NOT NULL,
                target_url VARCHAR(255),
                method VARCHAR(10) DEFAULT 'POST',
                headers_config TEXT,
                params_config TEXT,
                auth_config TEXT,
                auth_required BOOLEAN DEFAULT 1,
                membership_required BOOLEAN DEFAULT 0,
                rate_limit INTEGER DEFAULT 100,
                call_count INTEGER DEFAULT 0,
                success_count INTEGER DEFAULT 0,
                error_count INTEGER DEFAULT 0,
                created_at DATETIME,
                updated_at DATETIME,
                is_active BOOLEAN DEFAULT 1,
                FOREIGN KEY (software_id) REFERENCES software (id)
            )
        ''')
        print("  ✅ api_endpoints表创建完成")

        # 3. 创建api_call_logs表
        print("📝 创建api_call_logs表...")
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS api_call_logs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                software_id INTEGER NOT NULL,
                endpoint_id INTEGER NOT NULL,
                request_method VARCHAR(10),
                request_url VARCHAR(255),
                request_headers TEXT,
                request_body TEXT,
                response_status INTEGER,
                response_headers TEXT,
                response_body TEXT,
                response_time REAL,
                status VARCHAR(20) DEFAULT 'success',
                error_message TEXT,
                created_at DATETIME,
                FOREIGN KEY (user_id) REFERENCES users (id),
                FOREIGN KEY (software_id) REFERENCES software (id),
                FOREIGN KEY (endpoint_id) REFERENCES api_endpoints (id)
            )
        ''')
        print("  ✅ api_call_logs表创建完成")

        # 4. 创建索引
        print("📝 创建索引...")
        indexes = [
            ('idx_api_endpoints_software_id', 'api_endpoints', 'software_id'),
            ('idx_api_endpoints_type', 'api_endpoints', 'endpoint_type'),
            ('idx_api_call_logs_user_id', 'api_call_logs', 'user_id'),
            ('idx_api_call_logs_software_id', 'api_call_logs', 'software_id'),
            ('idx_api_call_logs_endpoint_id', 'api_call_logs', 'endpoint_id'),
            ('idx_api_call_logs_created_at', 'api_call_logs', 'created_at'),
            ('idx_api_call_logs_status', 'api_call_logs', 'status')
        ]

        for index_name, table_name, column_name in indexes:
            try:
                cursor.execute(f'CREATE INDEX IF NOT EXISTS {index_name} ON {table_name} ({column_name})')
                print(f"  ✅ 创建索引: {index_name}")
            except sqlite3.Error as e:
                print(f"  ⚠️ 索引创建失败: {index_name} - {e}")

        # 5. 插入示例数据
        print("📝 插入示例API端点...")

        # 检查是否已有软件数据
        cursor.execute("SELECT id, name FROM software LIMIT 5")
        software_list = cursor.fetchall()

        if software_list:
            # 为现有软件添加示例API端点
            for software_id, software_name in software_list:
                # 检查是否已有端点
                cursor.execute("SELECT COUNT(*) FROM api_endpoints WHERE software_id = ?", (software_id,))
                if cursor.fetchone()[0] == 0:
                    # 添加示例端点
                    sample_endpoints = [
                        {
                            'name': 'AI文本生成',
                            'description': '调用大模型生成文本内容',
                            'endpoint_type': 'ai_service',
                            'target_url': 'https://api.openai.com/v1/chat/completions',
                            'method': 'POST',
                            'headers_config': '{"Content-Type": "application/json"}',
                            'auth_config': '{"type": "bearer", "token": "your-api-key"}',
                            'membership_required': 1
                        },
                        {
                            'name': '图片生成',
                            'description': 'Midjourney图片生成服务',
                            'endpoint_type': 'ai_service',
                            'target_url': 'https://xuedingmao.online/mj/submit/imagine',
                            'method': 'POST',
                            'headers_config': '{"Content-Type": "application/json"}',
                            'auth_config': '{"type": "bearer", "token": "sk-435z6Pm6jimUgx5LhNHGAH7zZOjXLbnEsWT9CE84BjeonfvF"}',
                            'membership_required': 1
                        }
                    ]

                    for endpoint in sample_endpoints:
                        cursor.execute('''
                            INSERT INTO api_endpoints
                            (software_id, name, description, endpoint_type, target_url, method,
                             headers_config, auth_config, membership_required, created_at)
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                        ''', (
                            software_id,
                            endpoint['name'],
                            endpoint['description'],
                            endpoint['endpoint_type'],
                            endpoint['target_url'],
                            endpoint['method'],
                            endpoint['headers_config'],
                            endpoint['auth_config'],
                            endpoint['membership_required'],
                            datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                        ))

                    print(f"  ✅ 为软件 '{software_name}' 添加了示例API端点")

        # 6. 更新现有软件的类型
        print("📝 更新现有软件类型...")
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        cursor.execute('''
            UPDATE software
            SET software_type = 'hybrid',
                category = 'api_tool',
                updated_at = ?
            WHERE software_type IS NULL OR software_type = 'download'
        ''', (current_time,))

        # 提交事务
        conn.commit()
        print("✅ 数据库迁移完成！")

        # 显示统计信息
        cursor.execute("SELECT COUNT(*) FROM api_endpoints")
        endpoint_count = cursor.fetchone()[0]

        cursor.execute("SELECT COUNT(*) FROM software")
        software_count = cursor.fetchone()[0]

        print(f"\n📊 迁移统计:")
        print(f"  软件总数: {software_count}")
        print(f"  API端点总数: {endpoint_count}")

        return True

    except sqlite3.Error as e:
        print(f"❌ 数据库迁移失败: {e}")
        if conn:
            conn.rollback()
        return False
    finally:
        if conn:
            conn.close()

def main():
    """主函数"""
    print("🚀 XHS后端管理系统 - API服务数据库迁移")
    print("=" * 60)

    if migrate_database():
        print("\n🎉 迁移成功完成！")
        print("\n📋 新功能说明:")
        print("1. 软件现在支持API服务类型")
        print("2. 可以为软件配置多个API端点")
        print("3. 支持API调用日志记录")
        print("4. 支持频率限制和权限控制")
        print("5. 支持多种认证方式")

        print("\n🔗 新增API接口:")
        print("- GET  /api/service/software/{id}/endpoints - 获取软件API端点")
        print("- POST /api/service/call/{endpoint_id} - 调用API端点")
        print("- GET  /api/service/logs - 获取API调用日志")
        print("- GET  /api/service/stats - 获取API使用统计")

        print("\n👨‍💼 管理员接口:")
        print("- GET    /api/admin/software/{id}/endpoints - 管理API端点")
        print("- POST   /api/admin/software/{id}/endpoints - 创建API端点")
        print("- PUT    /api/admin/endpoints/{id} - 更新API端点")
        print("- DELETE /api/admin/endpoints/{id} - 删除API端点")
        print("- GET    /api/admin/api-logs - 查看API调用日志")

    else:
        print("\n❌ 迁移失败，请检查错误信息")
        sys.exit(1)

if __name__ == "__main__":
    main()
