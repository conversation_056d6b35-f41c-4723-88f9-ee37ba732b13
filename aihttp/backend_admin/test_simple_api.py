#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单API测试 - 测试基本功能
"""
from flask import Flask, jsonify, request
from flask_jwt_extended import JWTManager, jwt_required, get_jwt_identity
import sqlite3
import json

app = Flask(__name__)
app.config['JWT_SECRET_KEY'] = 'xhs-jwt-secret-key-2024'
jwt = JWTManager(app)

@app.route('/test/software/list')
@jwt_required()
def test_software_list():
    """测试软件列表API"""
    try:
        # 直接查询数据库
        conn = sqlite3.connect('data/data.sqlite')
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT id, name, description, software_type, category, 
                   auth_required, membership_required, download_count, 
                   api_call_count, created_at, updated_at, is_active
            FROM software 
            WHERE is_active = 1
        """)
        
        rows = cursor.fetchall()
        
        software_list = []
        for row in rows:
            software = {
                "id": row[0],
                "name": row[1],
                "description": row[2],
                "software_type": row[3],
                "category": row[4],
                "auth_required": bool(row[5]),
                "membership_required": bool(row[6]),
                "download_count": row[7] or 0,
                "api_call_count": row[8] or 0,
                "created_at": row[9] or "",
                "updated_at": row[10] or "",
                "is_active": bool(row[11])
            }
            software_list.append(software)
        
        conn.close()
        
        return jsonify({
            "success": True,
            "software": software_list,
            "count": len(software_list)
        })
        
    except Exception as e:
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

@app.route('/test/endpoints/<int:software_id>')
@jwt_required()
def test_endpoints(software_id):
    """测试API端点列表"""
    try:
        conn = sqlite3.connect('data/data.sqlite')
        cursor = conn.cursor()
        
        # 获取软件信息
        cursor.execute("SELECT id, name FROM software WHERE id = ? AND is_active = 1", (software_id,))
        software_row = cursor.fetchone()
        
        if not software_row:
            return jsonify({"success": False, "message": "软件不存在"}), 404
        
        # 获取API端点
        cursor.execute("""
            SELECT id, name, description, endpoint_type, target_url, method,
                   auth_required, membership_required, rate_limit, call_count,
                   success_count, error_count, created_at, is_active
            FROM api_endpoints 
            WHERE software_id = ? AND is_active = 1
        """, (software_id,))
        
        endpoint_rows = cursor.fetchall()
        
        endpoints = []
        for row in endpoint_rows:
            endpoint = {
                "id": row[0],
                "name": row[1],
                "description": row[2],
                "endpoint_type": row[3],
                "target_url": row[4],
                "method": row[5],
                "auth_required": bool(row[6]),
                "membership_required": bool(row[7]),
                "rate_limit": row[8] or 100,
                "call_count": row[9] or 0,
                "success_count": row[10] or 0,
                "error_count": row[11] or 0,
                "created_at": row[12] or "",
                "is_active": bool(row[13])
            }
            endpoints.append(endpoint)
        
        conn.close()
        
        return jsonify({
            "success": True,
            "software": {
                "id": software_row[0],
                "name": software_row[1]
            },
            "endpoints": endpoints,
            "count": len(endpoints)
        })
        
    except Exception as e:
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

@app.route('/test/create-endpoint/<int:software_id>', methods=['POST'])
@jwt_required()
def test_create_endpoint(software_id):
    """测试创建API端点"""
    try:
        data = request.get_json()
        
        conn = sqlite3.connect('data/data.sqlite')
        cursor = conn.cursor()
        
        # 插入新端点
        cursor.execute("""
            INSERT INTO api_endpoints 
            (software_id, name, description, endpoint_type, target_url, method,
             headers_config, params_config, auth_config, auth_required, 
             membership_required, rate_limit, created_at, is_active)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, datetime('now'), 1)
        """, (
            software_id,
            data.get('name', ''),
            data.get('description', ''),
            data.get('endpoint_type', 'custom'),
            data.get('target_url', ''),
            data.get('method', 'POST'),
            json.dumps(data.get('headers_config', {})),
            json.dumps(data.get('params_config', {})),
            json.dumps(data.get('auth_config', {})),
            data.get('auth_required', True),
            data.get('membership_required', False),
            data.get('rate_limit', 100)
        ))
        
        endpoint_id = cursor.lastrowid
        conn.commit()
        conn.close()
        
        return jsonify({
            "success": True,
            "message": "API端点创建成功",
            "endpoint_id": endpoint_id
        }), 201
        
    except Exception as e:
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5001, debug=True)
