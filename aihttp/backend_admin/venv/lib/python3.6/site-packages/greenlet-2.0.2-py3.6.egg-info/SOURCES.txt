.clang-format
.gitignore
.pylintrc
.readthedocs.yml
AUTHORS
CHANGES.rst
LICENSE
LICENSE.PSF
MANIFEST.in
README.rst
appveyor.yml
dev-requirements.txt
make-manylinux
pip-delete-this-directory.txt
setup.cfg
setup.py
tox.ini
.github/workflows/tests.yml
appveyor/install.ps1
appveyor/run_with_env.cmd
benchmarks/chain.py
docs/Makefile
docs/api.rst
docs/c_api.rst
docs/caveats.rst
docs/changes.rst
docs/conf.py
docs/contextvars.rst
docs/creating_executing_greenlets.rst
docs/development.rst
docs/greenlet.rst
docs/greenlet_gc.rst
docs/gui_example.rst
docs/history.rst
docs/index.rst
docs/make.bat
docs/python_threads.rst
docs/switching.rst
docs/tracing.rst
src/greenlet/__init__.py
src/greenlet/greenlet.cpp
src/greenlet/greenlet.h
src/greenlet/greenlet_allocator.hpp
src/greenlet/greenlet_compiler_compat.hpp
src/greenlet/greenlet_cpython_compat.hpp
src/greenlet/greenlet_exceptions.hpp
src/greenlet/greenlet_greenlet.hpp
src/greenlet/greenlet_internal.hpp
src/greenlet/greenlet_refs.hpp
src/greenlet/greenlet_slp_switch.hpp
src/greenlet/greenlet_thread_state.hpp
src/greenlet/greenlet_thread_state_dict_cleanup.hpp
src/greenlet/greenlet_thread_support.hpp
src/greenlet/slp_platformselect.h
src/greenlet.egg-info/PKG-INFO
src/greenlet.egg-info/SOURCES.txt
src/greenlet.egg-info/dependency_links.txt
src/greenlet.egg-info/not-zip-safe
src/greenlet.egg-info/requires.txt
src/greenlet.egg-info/top_level.txt
src/greenlet/platform/__init__.py
src/greenlet/platform/setup_switch_x64_masm.cmd
src/greenlet/platform/switch_aarch64_gcc.h
src/greenlet/platform/switch_alpha_unix.h
src/greenlet/platform/switch_amd64_unix.h
src/greenlet/platform/switch_arm32_gcc.h
src/greenlet/platform/switch_arm32_ios.h
src/greenlet/platform/switch_arm64_masm.asm
src/greenlet/platform/switch_arm64_masm.obj
src/greenlet/platform/switch_arm64_msvc.h
src/greenlet/platform/switch_csky_gcc.h
src/greenlet/platform/switch_m68k_gcc.h
src/greenlet/platform/switch_mips_unix.h
src/greenlet/platform/switch_ppc64_aix.h
src/greenlet/platform/switch_ppc64_linux.h
src/greenlet/platform/switch_ppc_aix.h
src/greenlet/platform/switch_ppc_linux.h
src/greenlet/platform/switch_ppc_macosx.h
src/greenlet/platform/switch_ppc_unix.h
src/greenlet/platform/switch_riscv_unix.h
src/greenlet/platform/switch_s390_unix.h
src/greenlet/platform/switch_sparc_sun_gcc.h
src/greenlet/platform/switch_x32_unix.h
src/greenlet/platform/switch_x64_masm.asm
src/greenlet/platform/switch_x64_masm.obj
src/greenlet/platform/switch_x64_msvc.h
src/greenlet/platform/switch_x86_msvc.h
src/greenlet/platform/switch_x86_unix.h
src/greenlet/tests/__init__.py
src/greenlet/tests/_test_extension.c
src/greenlet/tests/_test_extension_cpp.cpp
src/greenlet/tests/leakcheck.py
src/greenlet/tests/test_contextvars.py
src/greenlet/tests/test_cpp.py
src/greenlet/tests/test_extension_interface.py
src/greenlet/tests/test_gc.py
src/greenlet/tests/test_generator.py
src/greenlet/tests/test_generator_nested.py
src/greenlet/tests/test_greenlet.py
src/greenlet/tests/test_greenlet_trash.py
src/greenlet/tests/test_leaks.py
src/greenlet/tests/test_stack_saved.py
src/greenlet/tests/test_throw.py
src/greenlet/tests/test_tracing.py
src/greenlet/tests/test_version.py
src/greenlet/tests/test_weakref.py