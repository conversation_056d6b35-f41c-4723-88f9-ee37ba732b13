Metadata-Version: 2.1
Name: Mako
Version: 1.1.6
Summary: A super-fast templating language that borrows the  best ideas from the existing templating languages.
Home-page: https://www.makotemplates.org/
Author: <PERSON>
Author-email: <EMAIL>
License: MIT
Project-URL: Documentation, https://docs.makotemplates.org
Project-URL: Issue Tracker, https://github.com/sqlalchemy/mako
Keywords: templates
Platform: UNKNOWN
Classifier: Development Status :: 5 - Production/Stable
Classifier: License :: OSI Approved :: MIT License
Classifier: Environment :: Web Environment
Classifier: Intended Audience :: Developers
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.6
Classifier: Programming Language :: Python :: 3.7
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: Implementation :: CPython
Classifier: Programming Language :: Python :: Implementation :: PyPy
Classifier: Topic :: Internet :: WWW/HTTP :: Dynamic Content
Requires-Python: >=2.7, !=3.0.*, !=3.1.*, !=3.2.*, !=3.3.*
License-File: LICENSE
License-File: AUTHORS
Requires-Dist: MarkupSafe (>=0.9.2)
Provides-Extra: babel
Requires-Dist: Babel ; extra == 'babel'
Provides-Extra: lingua
Requires-Dist: lingua ; extra == 'lingua'

=========================
Mako Templates for Python
=========================

Mako is a template library written in Python. It provides a familiar, non-XML 
syntax which compiles into Python modules for maximum performance. Mako's 
syntax and API borrows from the best ideas of many others, including Django
templates, Cheetah, Myghty, and Genshi. Conceptually, Mako is an embedded 
Python (i.e. Python Server Page) language, which refines the familiar ideas
of componentized layout and inheritance to produce one of the most 
straightforward and flexible models available, while also maintaining close 
ties to Python calling and scoping semantics.

Nutshell
========

::

    <%inherit file="base.html"/>
    <%
        rows = [[v for v in range(0,10)] for row in range(0,10)]
    %>
    <table>
        % for row in rows:
            ${makerow(row)}
        % endfor
    </table>

    <%def name="makerow(row)">
        <tr>
        % for name in row:
            <td>${name}</td>\
        % endfor
        </tr>
    </%def>

Philosophy
===========

Python is a great scripting language. Don't reinvent the wheel...your templates can handle it !

Documentation
==============

See documentation for Mako at https://docs.makotemplates.org/en/latest/

License
========

Mako is licensed under an MIT-style license (see LICENSE).
Other incorporated projects may be licensed under different licenses.
All licenses allow for non-commercial and commercial use.


