#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新的API服务功能
"""
import requests
import json
import time

BASE_URL = "http://121.43.166.6:5000"

def test_login():
    """测试登录"""
    print("🔐 测试登录...")
    response = requests.post(f"{BASE_URL}/api/auth/login", json={
        "username": "admin",
        "password": "admin123"
    })
    
    if response.status_code == 200:
        data = response.json()
        print("✅ 登录成功")
        return data['access_token']
    else:
        print(f"❌ 登录失败: {response.text}")
        return None

def test_software_list(token):
    """测试获取软件列表"""
    print("\n📋 测试获取软件列表...")
    headers = {"Authorization": f"Bearer {token}"}
    
    response = requests.get(f"{BASE_URL}/api/software/list", headers=headers)
    
    if response.status_code == 200:
        data = response.json()
        print(f"✅ 获取软件列表成功，共 {len(data['software'])} 个软件")
        return data['software']
    else:
        print(f"❌ 获取软件列表失败: {response.text}")
        return []

def test_software_endpoints(token, software_id):
    """测试获取软件API端点"""
    print(f"\n🔗 测试获取软件 {software_id} 的API端点...")
    headers = {"Authorization": f"Bearer {token}"}
    
    response = requests.get(f"{BASE_URL}/api/service/software/{software_id}/endpoints", headers=headers)
    
    if response.status_code == 200:
        data = response.json()
        print(f"✅ 获取API端点成功，共 {len(data['endpoints'])} 个端点")
        for endpoint in data['endpoints']:
            print(f"  - {endpoint['name']} ({endpoint['endpoint_type']})")
        return data['endpoints']
    else:
        print(f"❌ 获取API端点失败: {response.text}")
        return []

def test_create_endpoint(token, software_id):
    """测试创建API端点"""
    print(f"\n➕ 测试为软件 {software_id} 创建API端点...")
    headers = {"Authorization": f"Bearer {token}"}
    
    endpoint_data = {
        "name": "测试GPT API",
        "description": "测试用的GPT文本生成API",
        "endpoint_type": "ai_service",
        "target_url": "https://api.openai.com/v1/chat/completions",
        "method": "POST",
        "headers_config": {
            "Content-Type": "application/json"
        },
        "auth_config": {
            "type": "bearer",
            "token": "sk-test-key"
        },
        "params_config": {
            "model": "gpt-3.5-turbo",
            "max_tokens": 100
        },
        "rate_limit": 50,
        "membership_required": True
    }
    
    response = requests.post(
        f"{BASE_URL}/api/admin/software/{software_id}/endpoints",
        headers=headers,
        json=endpoint_data
    )
    
    if response.status_code == 201:
        data = response.json()
        print(f"✅ 创建API端点成功: {data['endpoint']['name']}")
        return data['endpoint']['id']
    else:
        print(f"❌ 创建API端点失败: {response.text}")
        return None

def test_call_api(token, endpoint_id):
    """测试调用API端点"""
    print(f"\n🚀 测试调用API端点 {endpoint_id}...")
    headers = {"Authorization": f"Bearer {token}"}
    
    call_data = {
        "messages": [
            {"role": "user", "content": "Hello, how are you?"}
        ],
        "temperature": 0.7
    }
    
    response = requests.post(
        f"{BASE_URL}/api/service/call/{endpoint_id}",
        headers=headers,
        json=call_data
    )
    
    print(f"响应状态码: {response.status_code}")
    print(f"响应内容: {response.text[:500]}...")
    
    return response.status_code < 400

def test_api_stats(token):
    """测试获取API统计"""
    print("\n📊 测试获取API统计...")
    headers = {"Authorization": f"Bearer {token}"}
    
    response = requests.get(f"{BASE_URL}/api/service/stats", headers=headers)
    
    if response.status_code == 200:
        data = response.json()
        print("✅ 获取API统计成功:")
        stats = data['stats']
        print(f"  总调用次数: {stats['total_calls']}")
        print(f"  成功率: {stats['success_rate']}%")
        print(f"  今日调用: {stats['today_calls']}")
        return True
    else:
        print(f"❌ 获取API统计失败: {response.text}")
        return False

def test_api_logs(token):
    """测试获取API日志"""
    print("\n📋 测试获取API日志...")
    headers = {"Authorization": f"Bearer {token}"}
    
    response = requests.get(f"{BASE_URL}/api/service/logs?per_page=5", headers=headers)
    
    if response.status_code == 200:
        data = response.json()
        print(f"✅ 获取API日志成功，共 {data['pagination']['total']} 条记录")
        for log in data['logs'][:3]:  # 显示前3条
            print(f"  - {log['created_at']}: {log['status']}")
        return True
    else:
        print(f"❌ 获取API日志失败: {response.text}")
        return False

def main():
    """主测试函数"""
    print("🚀 XHS后端管理系统 - 新API功能测试")
    print("=" * 60)
    
    # 1. 登录获取token
    token = test_login()
    if not token:
        print("❌ 无法获取访问令牌，测试终止")
        return
    
    # 2. 获取软件列表
    software_list = test_software_list(token)
    if not software_list:
        print("❌ 没有可用的软件，测试终止")
        return
    
    software_id = software_list[0]['id']
    
    # 3. 获取软件API端点
    endpoints = test_software_endpoints(token, software_id)
    
    # 4. 创建新的API端点
    new_endpoint_id = test_create_endpoint(token, software_id)
    
    # 5. 如果有端点，测试调用
    if endpoints or new_endpoint_id:
        endpoint_id = new_endpoint_id or endpoints[0]['id']
        test_call_api(token, endpoint_id)
    
    # 6. 测试统计和日志
    test_api_stats(token)
    test_api_logs(token)
    
    print("\n" + "=" * 60)
    print("🎉 API功能测试完成！")
    
    print("\n📋 新功能总结:")
    print("1. ✅ 软件现在支持API服务类型")
    print("2. ✅ 可以为软件配置多个API端点")
    print("3. ✅ 支持API调用中转功能")
    print("4. ✅ 支持API调用日志记录")
    print("5. ✅ 支持API使用统计")
    print("6. ✅ 支持频率限制和权限控制")
    
    print("\n🔗 可用的API接口:")
    print("- GET  /api/service/software/{id}/endpoints - 获取软件API端点")
    print("- POST /api/service/call/{endpoint_id} - 调用API端点")
    print("- GET  /api/service/logs - 获取API调用日志")
    print("- GET  /api/service/stats - 获取API使用统计")
    print("- POST /api/admin/software/{id}/endpoints - 创建API端点")

if __name__ == "__main__":
    main()
