#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
XHS后端管理系统 - 客户端SDK示例
支持前端、桌面软件、移动应用等各种客户端调用API服务
"""
import requests
import json
import time
from typing import Dict, List, Optional, Any
from datetime import datetime

class XHSAPIClient:
    """XHS后端管理系统API客户端"""
    
    def __init__(self, base_url: str, username: str = None, password: str = None, token: str = None):
        """
        初始化API客户端
        
        Args:
            base_url: API基础URL，如 http://ai.http28.com:5000
            username: 用户名（用于登录获取token）
            password: 密码
            token: 已有的访问令牌
        """
        self.base_url = base_url.rstrip('/')
        self.token = token
        self.session = requests.Session()
        
        if username and password and not token:
            self.login(username, password)
    
    def login(self, username: str, password: str) -> Dict[str, Any]:
        """用户登录获取访问令牌"""
        response = self.session.post(
            f"{self.base_url}/api/auth/login",
            json={"username": username, "password": password}
        )
        
        if response.status_code == 200:
            data = response.json()
            self.token = data.get('access_token')
            return data
        else:
            raise Exception(f"登录失败: {response.text}")
    
    def _get_headers(self) -> Dict[str, str]:
        """获取请求头"""
        headers = {"Content-Type": "application/json"}
        if self.token:
            headers["Authorization"] = f"Bearer {self.token}"
        return headers
    
    def get_software_list(self) -> List[Dict[str, Any]]:
        """获取软件列表"""
        response = self.session.get(
            f"{self.base_url}/api/software/list",
            headers=self._get_headers()
        )
        
        if response.status_code == 200:
            return response.json().get('software', [])
        else:
            raise Exception(f"获取软件列表失败: {response.text}")
    
    def get_software_endpoints(self, software_id: int) -> Dict[str, Any]:
        """获取软件的API端点列表"""
        response = self.session.get(
            f"{self.base_url}/api/service/software/{software_id}/endpoints",
            headers=self._get_headers()
        )
        
        if response.status_code == 200:
            return response.json()
        else:
            raise Exception(f"获取API端点失败: {response.text}")
    
    def call_api(self, endpoint_id: int, data: Dict[str, Any]) -> Dict[str, Any]:
        """调用API端点"""
        start_time = time.time()
        
        response = self.session.post(
            f"{self.base_url}/api/service/call/{endpoint_id}",
            headers=self._get_headers(),
            json=data
        )
        
        response_time = time.time() - start_time
        
        result = {
            "status_code": response.status_code,
            "response_time": response_time,
            "success": response.status_code < 400
        }
        
        try:
            result["data"] = response.json()
        except:
            result["data"] = {"raw_response": response.text}
        
        return result
    
    def get_api_logs(self, page: int = 1, per_page: int = 20, **filters) -> Dict[str, Any]:
        """获取API调用日志"""
        params = {"page": page, "per_page": per_page}
        params.update(filters)
        
        response = self.session.get(
            f"{self.base_url}/api/service/logs",
            headers=self._get_headers(),
            params=params
        )
        
        if response.status_code == 200:
            return response.json()
        else:
            raise Exception(f"获取API日志失败: {response.text}")
    
    def get_api_stats(self) -> Dict[str, Any]:
        """获取API使用统计"""
        response = self.session.get(
            f"{self.base_url}/api/service/stats",
            headers=self._get_headers()
        )
        
        if response.status_code == 200:
            return response.json()
        else:
            raise Exception(f"获取API统计失败: {response.text}")

class AIServiceHelper:
    """AI服务助手类 - 封装常用AI服务调用"""
    
    def __init__(self, client: XHSAPIClient):
        self.client = client
        self.endpoints_cache = {}
    
    def _get_endpoint_by_type(self, software_id: int, endpoint_type: str) -> Optional[int]:
        """根据类型获取端点ID"""
        if software_id not in self.endpoints_cache:
            endpoints_data = self.client.get_software_endpoints(software_id)
            self.endpoints_cache[software_id] = endpoints_data.get('endpoints', [])
        
        for endpoint in self.endpoints_cache[software_id]:
            if endpoint.get('endpoint_type') == endpoint_type:
                return endpoint['id']
        
        return None
    
    def generate_text(self, software_id: int, prompt: str, **kwargs) -> str:
        """生成文本内容"""
        endpoint_id = self._get_endpoint_by_type(software_id, 'ai_service')
        if not endpoint_id:
            raise Exception("未找到文本生成API端点")
        
        data = {"prompt": prompt}
        data.update(kwargs)
        
        result = self.client.call_api(endpoint_id, data)
        
        if result['success']:
            # 根据不同AI服务的响应格式解析结果
            response_data = result['data'].get('response', {}).get('data', {})
            
            # OpenAI格式
            if 'choices' in response_data:
                return response_data['choices'][0].get('message', {}).get('content', '')
            
            # 其他格式
            return str(response_data)
        else:
            raise Exception(f"文本生成失败: {result['data']}")
    
    def generate_image(self, software_id: int, prompt: str, **kwargs) -> str:
        """生成图片"""
        endpoint_id = self._get_endpoint_by_type(software_id, 'ai_service')
        if not endpoint_id:
            raise Exception("未找到图片生成API端点")
        
        data = {"prompt": prompt}
        data.update(kwargs)
        
        result = self.client.call_api(endpoint_id, data)
        
        if result['success']:
            response_data = result['data'].get('response', {}).get('data', {})
            
            # Midjourney格式
            if 'result' in response_data:
                return response_data['result']
            
            # 其他格式
            return str(response_data)
        else:
            raise Exception(f"图片生成失败: {result['data']}")

# 使用示例
def example_usage():
    """使用示例"""
    
    # 1. 初始化客户端
    client = XHSAPIClient(
        base_url="http://ai.http28.com:5000",
        username="your_username",
        password="your_password"
    )
    
    print("✅ 客户端初始化成功")
    
    # 2. 获取软件列表
    software_list = client.get_software_list()
    print(f"📋 可用软件数量: {len(software_list)}")
    
    for software in software_list[:3]:  # 显示前3个
        print(f"  - {software['name']} ({software['software_type']})")
    
    if not software_list:
        print("⚠️ 没有可用的软件")
        return
    
    # 3. 选择第一个软件，获取其API端点
    software_id = software_list[0]['id']
    endpoints_data = client.get_software_endpoints(software_id)
    
    print(f"\n🔗 软件 '{endpoints_data['software']['name']}' 的API端点:")
    for endpoint in endpoints_data['endpoints']:
        print(f"  - {endpoint['name']} ({endpoint['endpoint_type']})")
    
    # 4. 使用AI服务助手
    ai_helper = AIServiceHelper(client)
    
    try:
        # 生成文本
        text_result = ai_helper.generate_text(
            software_id=software_id,
            prompt="写一首关于人工智能的诗",
            max_tokens=100
        )
        print(f"\n📝 生成的文本:\n{text_result}")
        
    except Exception as e:
        print(f"❌ 文本生成失败: {e}")
    
    try:
        # 生成图片
        image_result = ai_helper.generate_image(
            software_id=software_id,
            prompt="a beautiful sunset over mountains"
        )
        print(f"\n🖼️ 生成的图片: {image_result}")
        
    except Exception as e:
        print(f"❌ 图片生成失败: {e}")
    
    # 5. 查看API使用统计
    try:
        stats = client.get_api_stats()
        print(f"\n📊 API使用统计:")
        print(f"  总调用次数: {stats['stats']['total_calls']}")
        print(f"  成功率: {stats['stats']['success_rate']}%")
        print(f"  今日调用: {stats['stats']['today_calls']}")
        
    except Exception as e:
        print(f"❌ 获取统计失败: {e}")
    
    # 6. 查看最近的API调用日志
    try:
        logs = client.get_api_logs(page=1, per_page=5)
        print(f"\n📋 最近的API调用:")
        for log in logs['logs']:
            print(f"  - {log['created_at']}: {log['software']['name']} - {log['status']}")
            
    except Exception as e:
        print(f"❌ 获取日志失败: {e}")

# 前端JavaScript示例
def generate_javascript_example():
    """生成前端JavaScript调用示例"""
    js_code = '''
// XHS API客户端 - JavaScript版本
class XHSAPIClient {
    constructor(baseUrl, token) {
        this.baseUrl = baseUrl.replace(/\/$/, '');
        this.token = token;
    }
    
    async login(username, password) {
        const response = await fetch(`${this.baseUrl}/api/auth/login`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ username, password })
        });
        
        if (response.ok) {
            const data = await response.json();
            this.token = data.access_token;
            return data;
        } else {
            throw new Error(`登录失败: ${await response.text()}`);
        }
    }
    
    async callAPI(endpointId, data) {
        const response = await fetch(`${this.baseUrl}/api/service/call/${endpointId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${this.token}`
            },
            body: JSON.stringify(data)
        });
        
        return await response.json();
    }
    
    async getSoftwareEndpoints(softwareId) {
        const response = await fetch(`${this.baseUrl}/api/service/software/${softwareId}/endpoints`, {
            headers: { 'Authorization': `Bearer ${this.token}` }
        });
        
        return await response.json();
    }
}

// 使用示例
async function example() {
    const client = new XHSAPIClient('http://ai.http28.com:5000');
    
    // 登录
    await client.login('username', 'password');
    
    // 调用AI服务
    const result = await client.callAPI(1, {
        prompt: '写一篇关于AI的文章',
        max_tokens: 500
    });
    
    console.log('AI生成结果:', result);
}
'''
    
    with open('client_sdk_example.js', 'w', encoding='utf-8') as f:
        f.write(js_code)
    
    print("📄 已生成JavaScript客户端示例: client_sdk_example.js")

if __name__ == "__main__":
    print("🚀 XHS后端管理系统 - 客户端SDK示例")
    print("=" * 60)
    
    # 运行Python示例
    try:
        example_usage()
    except Exception as e:
        print(f"❌ 示例运行失败: {e}")
        print("请确保服务器正在运行且用户名密码正确")
    
    # 生成JavaScript示例
    generate_javascript_example()
    
    print("\n🎉 SDK示例演示完成！")
