# GPT-4o Image VIP 前端应用

## 🎨 项目简介

这是一个基于GPT-4o的AI图像生成前端应用，采用Discord风格的界面设计，支持用户登录认证和高质量图像生成功能。

## ✨ 主要特性

### 🔐 用户认证
- 集成后端管理系统的用户登录
- JWT Token认证机制
- 自动保存登录状态
- 安全的登出功能

### 🎨 图像生成
- 基于OpenAI DALL-E 3模型
- 支持多种图像尺寸（1024x1024, 1792x1024, 1024x1792）
- 可选择图像质量（标准/高清）
- 支持图像风格设置（生动/自然）
- 实时生成进度显示

### 🎯 Discord风格界面
- 左侧频道导航栏
- 多频道分类（通用生成、创意设计、照片风格、艺术创作）
- 消息气泡式对话界面
- 响应式设计，支持移动端

### 🛠️ 高级功能
- 图像下载功能
- 图像链接复制
- 图像预览和放大
- 输入框自动调整高度
- 键盘快捷键支持（Enter发送）

## 🚀 技术栈

- **前端**: 原生HTML5 + CSS3 + JavaScript (ES6+)
- **UI框架**: 无依赖，纯原生实现
- **图标**: Font Awesome 6.0
- **API**: OpenAI DALL-E 3 API
- **认证**: JWT Token
- **样式**: CSS Grid + Flexbox

## 📁 项目结构

```
gpt4o_image_frontend/
├── index.html          # 主页面
├── styles.css          # 样式文件
├── script.js           # JavaScript逻辑
└── README.md           # 项目说明
```

## 🔧 配置说明

### API配置
在 `script.js` 中的 `CONFIG` 对象：

```javascript
const CONFIG = {
    BACKEND_URL: 'http://************:5000',           // 后端API地址
    OPENAI_API_URL: 'https://xuedingmao.online/v1',    // OpenAI API地址
    OPENAI_API_KEY: 'sk-o6kaAOCW2lV5z5gf3CY7hlRYc5gSmcrOaS9L02tNfJdKCYhN'  // API密钥
};
```

### 后端依赖
- 需要运行后端管理系统提供用户认证
- 后端需要提供以下API端点：
  - `POST /api/auth/login` - 用户登录
  - `GET /api/auth/verify` - Token验证

## 🎮 使用方法

### 1. 启动应用
直接在浏览器中打开 `index.html` 文件，或部署到Web服务器。

### 2. 用户登录
- 输入用户名和密码
- 点击登录按钮
- 系统会验证用户身份并保存登录状态

### 3. 生成图像
1. 选择合适的频道（可选）
2. 在输入框中描述想要生成的图像
3. 点击设置按钮调整生成参数（可选）
4. 按Enter键或点击发送按钮开始生成
5. 等待AI生成完成，查看结果

### 4. 图像操作
- **预览**: 点击图像可在新窗口中查看
- **下载**: 点击下载按钮保存图像到本地
- **复制链接**: 点击复制按钮获取图像URL

## 🎨 设计特色

### 青绿色主题
- 主色调：#4ade80 (青绿色)
- 辅助色：#22c55e (深青绿)
- 背景色：深色系渐变

### Discord风格布局
- 左侧频道栏：280px宽度
- 主内容区：自适应宽度
- 消息气泡：左右对齐显示
- 输入区：底部固定

### 响应式设计
- 移动端适配
- 弹性布局
- 自适应字体大小

## 🔒 安全特性

- JWT Token认证
- 本地存储加密
- API密钥保护
- CORS跨域处理
- 输入验证和过滤

## 🚀 部署建议

### 开发环境
1. 直接打开HTML文件
2. 使用Live Server等工具
3. 确保后端服务正常运行

### 生产环境
1. 部署到Nginx/Apache
2. 配置HTTPS
3. 设置正确的CORS策略
4. 优化静态资源

## 🐛 常见问题

### 登录失败
- 检查后端服务是否运行
- 确认用户名密码正确
- 查看网络连接状态

### 图像生成失败
- 检查OpenAI API密钥
- 确认API配额充足
- 验证网络连接

### 界面显示异常
- 清除浏览器缓存
- 检查CSS文件加载
- 确认JavaScript无错误

## 📝 更新日志

### v1.0.0 (2025-05-25)
- ✅ 完成基础界面设计
- ✅ 实现用户登录认证
- ✅ 集成OpenAI图像生成
- ✅ 添加Discord风格布局
- ✅ 支持多频道切换
- ✅ 实现图像下载和分享

## 🤝 贡献指南

欢迎提交Issue和Pull Request来改进这个项目！

## 📄 许可证

MIT License

---

**GPT-4o Image VIP** - 让AI图像生成变得简单而优雅 🎨✨
