# Nginx配置文件 - ai.http28.com
# 将此文件复制到 /etc/nginx/sites-available/ 目录下

server {
    listen 80;
    server_name ai.http28.com www.ai.http28.com;

    # 重定向HTTP到HTTPS (可选)
    # return 301 https://$server_name$request_uri;

    # GPT-4o Image前端应用
    location /gpt4o/ {
        alias /www/wwwroot/ai.http28.com/gpt4o_image/;
        index index.html;
        try_files $uri $uri/ /gpt4o/index.html;

        # 静态文件缓存
        location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }

    # API代理 - 支持CORS
    location /api/ {
        proxy_pass http://127.0.0.1:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Port $server_port;

        # CORS设置
        add_header Access-Control-Allow-Origin *;
        add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS";
        add_header Access-Control-Allow-Headers "Content-Type, Authorization";

        if ($request_method = 'OPTIONS') {
            return 204;
        }

        # 超时设置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;

        # 缓冲设置
        proxy_buffering on;
        proxy_buffer_size 8k;
        proxy_buffers 8 8k;
    }

    # 或者直接处理HTTP请求 - 后端管理界面
    location / {
        proxy_pass http://127.0.0.1:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Port $server_port;

        # 超时设置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;

        # 缓冲设置
        proxy_buffering on;
        proxy_buffer_size 8k;
        proxy_buffers 8 8k;
    }

    # 静态文件处理
    location /static {
        alias /www/wwwroot/ai.http28.com/backend_admin/app/static;
        expires 30d;
        add_header Cache-Control "public, immutable";
        access_log off;
    }

    # 上传文件处理
    location /uploads {
        alias /www/wwwroot/ai.http28.com/backend_admin/app/uploads;
        expires 7d;
        add_header Cache-Control "public";
        access_log off;
    }

    # 安全头设置
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;

    # 日志设置
    access_log /var/log/nginx/ai_http28_access.log;
    error_log /var/log/nginx/ai_http28_error.log;

    # 文件上传大小限制
    client_max_body_size 100M;
}

# HTTPS配置 (使用SSL证书后启用)
# server {
#     listen 443 ssl http2;
#     server_name ai.http28.com www.ai.http28.com;
#
#     # SSL证书配置
#     ssl_certificate /path/to/ssl/certificate.crt;
#     ssl_certificate_key /path/to/ssl/private.key;
#
#     # SSL安全配置
#     ssl_protocols TLSv1.2 TLSv1.3;
#     ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384;
#     ssl_prefer_server_ciphers off;
#     ssl_session_cache shared:SSL:10m;
#     ssl_session_timeout 10m;
#
#     # 其他配置与HTTP相同
#     location / {
#         proxy_pass http://127.0.0.1:5000;
#         proxy_set_header Host $host;
#         proxy_set_header X-Real-IP $remote_addr;
#         proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
#         proxy_set_header X-Forwarded-Proto $scheme;
#         proxy_set_header X-Forwarded-Host $host;
#         proxy_set_header X-Forwarded-Port $server_port;
#     }
#
#     location /static {
#         alias /www/wwwroot/ai.http28.com/backend_admin/app/static;
#         expires 30d;
#         add_header Cache-Control "public, immutable";
#     }
#
#     location /uploads {
#         alias /www/wwwroot/ai.http28.com/backend_admin/app/uploads;
#         expires 7d;
#         add_header Cache-Control "public";
#     }
# }
