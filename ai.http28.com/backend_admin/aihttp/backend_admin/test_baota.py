#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
宝塔环境测试脚本
"""
import os
import sys

def test_baota_environment():
    """测试宝塔环境"""
    print("🔍 宝塔环境测试")
    print("=" * 50)
    
    # 测试Python版本
    print(f"Python版本: {sys.version}")
    
    # 测试项目路径
    project_path = os.path.dirname(os.path.abspath(__file__))
    print(f"项目路径: {project_path}")
    
    # 测试环境变量
    flask_config = os.environ.get('FLASK_CONFIG', 'development')
    print(f"Flask配置: {flask_config}")
    
    # 测试导入
    try:
        sys.path.insert(0, project_path)
        from app import create_app
        print("✅ 应用模块导入成功")
        
        # 创建应用实例
        app = create_app('baota')
        print("✅ 应用实例创建成功")
        
        # 测试数据库
        with app.app_context():
            from app import db
            from app.models import User
            
            # 检查数据库连接
            try:
                user_count = User.query.count()
                print(f"✅ 数据库连接正常，用户数: {user_count}")
            except Exception as e:
                print(f"❌ 数据库连接失败: {e}")
        
        print("✅ 宝塔环境测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

if __name__ == "__main__":
    success = test_baota_environment()
    if success:
        print("\n🎉 环境测试通过，可以在宝塔中部署！")
        print("\n📋 下一步操作:")
        print("1. 在宝塔面板中创建Python项目")
        print("2. 设置启动文件为: start_baota.py")
        print("3. 安装依赖包: pip install -r requirements.txt")
        print("4. 启动项目")
        print("5. 访问: http://你的域名或IP:5000")
    else:
        print("\n❌ 环境测试失败，请检查配置")
