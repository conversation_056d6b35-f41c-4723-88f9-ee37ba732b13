#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""创建示例数据脚本"""
from datetime import datetime, timedelta
from app import create_app, db
from app.models import User, Software, SoftwareVersion, MembershipPlan, MidjourneyTask

def create_sample_data():
    """创建示例数据"""
    app = create_app()
    
    with app.app_context():
        print("🔧 创建示例数据...")
        
        # 创建示例软件
        software_list = [
            {
                "name": "XHS自动发布工具",
                "description": "小红书内容自动发布工具，支持图文、视频批量发布，提高运营效率"
            },
            {
                "name": "AI图片生成器",
                "description": "基于Midjourney的AI图片生成工具，支持多种风格和参数调节"
            },
            {
                "name": "内容管理助手",
                "description": "多平台内容管理工具，支持小红书、抖音、微博等平台的内容同步发布"
            }
        ]
        
        for software_data in software_list:
            if not Software.query.filter_by(name=software_data["name"]).first():
                software = Software(**software_data)
                db.session.add(software)
                db.session.flush()  # 获取ID
                
                # 为每个软件创建版本
                versions = [
                    {
                        "version_number": "1.0.0",
                        "release_notes": "初始版本发布",
                        "is_external": True,
                        "external_url": "https://example.com/download/v1.0.0"
                    },
                    {
                        "version_number": "1.1.0",
                        "release_notes": "修复已知问题，新增批量处理功能",
                        "is_external": True,
                        "external_url": "https://example.com/download/v1.1.0"
                    },
                    {
                        "version_number": "1.2.0",
                        "release_notes": "优化用户界面，提升性能",
                        "is_external": True,
                        "external_url": "https://example.com/download/v1.2.0"
                    }
                ]
                
                for i, version_data in enumerate(versions):
                    version = SoftwareVersion(
                        software_id=software.id,
                        **version_data,
                        created_at=datetime.now() - timedelta(days=30-i*10)
                    )
                    db.session.add(version)
        
        # 创建示例用户
        sample_users = [
            {
                "username": "user001",
                "email": "<EMAIL>",
                "password": "password123",
                "is_paid_member": True,
                "membership_expiry": datetime.now() + timedelta(days=30)
            },
            {
                "username": "user002", 
                "email": "<EMAIL>",
                "password": "password123",
                "is_paid_member": True,
                "membership_expiry": datetime.now() + timedelta(days=90)
            },
            {
                "username": "user003",
                "email": "<EMAIL>", 
                "password": "password123",
                "is_paid_member": False
            },
            {
                "username": "user004",
                "email": "<EMAIL>",
                "password": "password123",
                "is_paid_member": True,
                "membership_expiry": datetime.now() - timedelta(days=10)  # 过期会员
            }
        ]
        
        for user_data in sample_users:
            if not User.query.filter_by(username=user_data["username"]).first():
                password = user_data.pop("password")
                user = User(**user_data)
                user.set_password(password)
                user.created_at = datetime.now() - timedelta(days=60)
                db.session.add(user)
        
        # 创建示例AI任务
        admin_user = User.query.filter_by(username="admin").first()
        if admin_user:
            sample_tasks = [
                {
                    "user_id": admin_user.id,
                    "task_id": "task_001",
                    "prompt": "一只可爱的小猫在花园里玩耍，卡通风格",
                    "status": "completed",
                    "result_url": "https://example.com/images/cat_garden.jpg"
                },
                {
                    "user_id": admin_user.id,
                    "task_id": "task_002", 
                    "prompt": "未来科技城市夜景，赛博朋克风格",
                    "status": "completed",
                    "result_url": "https://example.com/images/cyberpunk_city.jpg"
                },
                {
                    "user_id": admin_user.id,
                    "task_id": "task_003",
                    "prompt": "山水画风格的自然风景",
                    "status": "pending"
                }
            ]
            
            for task_data in sample_tasks:
                if not MidjourneyTask.query.filter_by(task_id=task_data["task_id"]).first():
                    task = MidjourneyTask(**task_data)
                    task.created_at = datetime.now() - timedelta(hours=24)
                    db.session.add(task)
        
        db.session.commit()
        
        print("✅ 示例数据创建完成!")
        print()
        print("📊 数据统计:")
        print(f"软件数量: {Software.query.count()}")
        print(f"软件版本数: {SoftwareVersion.query.count()}")
        print(f"用户数量: {User.query.count()}")
        print(f"会员计划数: {MembershipPlan.query.count()}")
        print(f"AI任务数: {MidjourneyTask.query.count()}")
        print()
        print("👥 用户列表:")
        for user in User.query.all():
            status = "管理员" if user.is_admin else ("有效会员" if user.is_membership_valid() else "普通用户")
            print(f"  - {user.username} ({user.email}) - {status}")
        print()
        print("💾 软件列表:")
        for software in Software.query.all():
            versions_count = software.versions.count()
            print(f"  - {software.name} ({versions_count}个版本)")

if __name__ == "__main__":
    create_sample_data()
