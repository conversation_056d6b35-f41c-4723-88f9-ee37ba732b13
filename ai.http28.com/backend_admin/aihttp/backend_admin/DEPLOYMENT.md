# XHS 后端管理系统部署指南

## 🎯 系统概述

XHS 后端管理系统是一个基于Flask的企业级后端管理平台，专为小红书自动发布和AI图片生成业务设计。

### 主要功能
- 🔐 JWT认证系统
- 👥 用户和会员管理
- 💾 软件版本管理和分发
- 🎨 Midjourney AI图片生成集成
- 📊 完整的Web管理后台
- 🔒 基于角色的权限控制

## 🚀 快速开始

### 方法一：使用启动脚本（推荐）

**Linux/macOS:**
```bash
chmod +x start.sh
./start.sh
```

**Windows:**
```cmd
start.bat
```

### 方法二：手动部署

#### 1. 环境要求
- Python 3.7+
- pip包管理器

#### 2. 安装依赖
```bash
pip install -r requirements.txt
```

#### 3. 初始化数据库
```bash
python create_admin.py
```

#### 4. 启动服务
```bash
python run.py
```

## 📋 访问信息

- **主页**: http://localhost:5000
- **管理后台**: http://localhost:5000/admin
- **默认管理员账号**: admin / admin123

## 🔧 配置说明

### 环境变量配置

创建 `.env` 文件：
```env
FLASK_CONFIG=production
SECRET_KEY=your-secret-key-here
JWT_SECRET_KEY=your-jwt-secret-key-here
DATABASE_URL=sqlite:///data/data.sqlite
MIDJOURNEY_API_KEY=your-midjourney-api-key
MIDJOURNEY_API_BASE=https://xuedingmao.online
```

### 配置文件说明

编辑 `config.py` 文件来修改配置：

```python
class ProductionConfig(Config):
    """生产环境配置"""
    DEBUG = False
    SQLALCHEMY_DATABASE_URI = "sqlite:///data/data.sqlite"
    MIDJOURNEY_API_KEY = "your-api-key"
    MIDJOURNEY_API_BASE = "https://xuedingmao.online"
```

## 🌐 生产环境部署

### 使用Gunicorn

1. 安装Gunicorn：
```bash
pip install gunicorn
```

2. 启动服务：
```bash
gunicorn -w 4 -b 0.0.0.0:5000 run:app
```

### 使用Nginx反向代理

1. 安装Nginx
2. 配置文件 `/etc/nginx/sites-available/xhs-backend`：

```nginx
server {
    listen 80;
    server_name ai.http28.com;

    location / {
        proxy_pass http://127.0.0.1:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    location /static {
        alias /www/wwwroot/ai.http28.com/backend_admin/app/static;
        expires 30d;
    }

    location /uploads {
        alias /www/wwwroot/ai.http28.com/backend_admin/app/uploads;
        expires 30d;
    }
}
```

3. 启用站点：
```bash
sudo ln -s /etc/nginx/sites-available/xhs-backend /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

### 使用Systemd服务

1. 创建服务文件 `/etc/systemd/system/xhs-backend.service`：

```ini
[Unit]
Description=XHS Backend Admin
After=network.target

[Service]
Type=simple
User=www-data
WorkingDirectory=/path/to/backend_admin
Environment=PATH=/path/to/backend_admin/venv/bin
ExecStart=/path/to/python run.py
Restart=always

[Install]
WantedBy=multi-user.target
```

2. 启用服务：
```bash
sudo systemctl enable xhs-backend
sudo systemctl start xhs-backend
sudo systemctl status xhs-backend
```

## 📊 API文档

### 认证接口

| 方法 | 端点 | 描述 |
|------|------|------|
| POST | `/api/auth/register` | 用户注册 |
| POST | `/api/auth/login` | 用户登录 |
| POST | `/api/auth/refresh` | 刷新令牌 |
| GET | `/api/auth/profile` | 获取用户信息 |
| PUT | `/api/auth/profile` | 更新用户信息 |

### 软件管理

| 方法 | 端点 | 描述 |
|------|------|------|
| GET | `/api/software/list` | 获取软件列表 |
| GET | `/api/software/{id}` | 获取软件详情 |
| GET | `/api/software/download/{version_id}` | 下载软件 |
| GET | `/api/software/check-update/{name}/{version}` | 检查更新 |

### AI图片生成

| 方法 | 端点 | 描述 |
|------|------|------|
| POST | `/api/midjourney/generate` | 生成AI图片 |
| GET | `/api/midjourney/tasks/{id}` | 获取任务状态 |
| GET | `/api/midjourney/tasks` | 获取任务列表 |

### 管理员接口

| 方法 | 端点 | 描述 |
|------|------|------|
| GET | `/admin/stats` | 获取统计数据 |
| GET | `/admin/users` | 用户管理 |
| GET | `/admin/software` | 软件管理 |
| GET | `/admin/membership-plans` | 会员计划管理 |

## 🔒 安全配置

### 1. 修改默认密码
```bash
# 登录管理后台后立即修改默认管理员密码
```

### 2. 配置防火墙
```bash
# Ubuntu/Debian
sudo ufw allow 22
sudo ufw allow 80
sudo ufw allow 443
sudo ufw enable

# CentOS/RHEL
sudo firewall-cmd --permanent --add-service=ssh
sudo firewall-cmd --permanent --add-service=http
sudo firewall-cmd --permanent --add-service=https
sudo firewall-cmd --reload
```

### 3. SSL证书配置
使用Let's Encrypt免费SSL证书：
```bash
sudo apt install certbot python3-certbot-nginx
sudo certbot --nginx -d ai.http28.com
```

## 📝 维护操作

### 数据库备份
```bash
# 备份SQLite数据库
cp data/data.sqlite data/backup_$(date +%Y%m%d_%H%M%S).sqlite
```

### 日志查看
```bash
# 查看应用日志
tail -f logs/backend_admin.log

# 查看系统服务日志
sudo journalctl -u xhs-backend -f
```

### 更新应用
```bash
# 停止服务
sudo systemctl stop xhs-backend

# 更新代码
git pull origin main

# 安装新依赖
pip install -r requirements.txt

# 数据库迁移（如有）
python -c "from app import create_app, db; app = create_app(); app.app_context().push(); db.create_all()"

# 重启服务
sudo systemctl start xhs-backend
```

## 🐛 故障排除

### 常见问题

1. **端口被占用**
```bash
# 查看端口占用
sudo netstat -tlnp | grep :5000
# 或
sudo lsof -i :5000
```

2. **数据库连接失败**
```bash
# 检查数据库文件权限
ls -la data/
# 修复权限
sudo chown -R www-data:www-data data/
```

3. **API调用失败**
- 检查API密钥配置
- 验证网络连接
- 查看API配额使用情况

### 日志级别配置

在 `config.py` 中设置日志级别：
```python
import logging
logging.basicConfig(level=logging.INFO)
```

## 📞 技术支持

如遇到问题，请检查：
1. 系统日志文件
2. 网络连接状态
3. 配置文件正确性
4. 依赖包版本兼容性

## 📄 许可证

本项目采用 MIT 许可证。
