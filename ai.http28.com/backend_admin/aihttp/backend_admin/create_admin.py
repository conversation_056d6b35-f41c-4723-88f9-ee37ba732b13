#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""创建管理员账号脚本"""
import sys
from app import create_app, db
from app.models import User, MembershipPlan

def create_admin_user():
    """创建管理员用户"""
    app = create_app()
    
    with app.app_context():
        # 创建数据库表
        db.create_all()
        print("数据库表创建完成")
        
        # 检查是否已有管理员
        admin = User.query.filter_by(is_admin=True).first()
        if admin:
            print(f"管理员账号已存在: {admin.username}")
            return
        
        # 创建默认管理员
        admin = User(
            username="admin",
            email="<EMAIL>",
            is_admin=True,
            is_paid_member=True
        )
        admin.set_password("admin123")
        
        db.session.add(admin)
        
        # 创建默认会员计划
        plans = [
            {
                "name": "月度会员",
                "description": "享受所有功能，月度订阅",
                "price": 29.9,
                "duration_months": 1
            },
            {
                "name": "季度会员",
                "description": "享受所有功能，季度订阅，更优惠",
                "price": 79.9,
                "duration_months": 3
            },
            {
                "name": "年度会员",
                "description": "享受所有功能，年度订阅，最优惠",
                "price": 299.9,
                "duration_months": 12
            }
        ]
        
        for plan_data in plans:
            if not MembershipPlan.query.filter_by(name=plan_data["name"]).first():
                plan = MembershipPlan(**plan_data)
                db.session.add(plan)
        
        db.session.commit()
        
        print("=" * 50)
        print("🎉 后端管理系统初始化完成!")
        print("=" * 50)
        print("管理员账号信息:")
        print(f"用户名: admin")
        print(f"邮箱: <EMAIL>")
        print(f"密码: admin123")
        print("=" * 50)
        print("访问地址:")
        print("API文档: http://localhost:5000/")
        print("管理后台: http://localhost:5000/admin")
        print("=" * 50)
        print("启动命令: python run.py")
        print("=" * 50)

if __name__ == "__main__":
    create_admin_user()
