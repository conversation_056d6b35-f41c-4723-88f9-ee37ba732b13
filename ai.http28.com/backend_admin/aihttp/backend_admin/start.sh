#!/bin/bash
# XHS 后端管理系统启动脚本

echo "🚀 启动 XHS 后端管理系统..."

# 检查Python是否安装
if ! command -v python3 &> /dev/null; then
    echo "❌ 错误: 未找到Python3，请先安装Python3"
    exit 1
fi

# 检查是否在正确的目录
if [ ! -f "run.py" ]; then
    echo "❌ 错误: 请在backend_admin目录下运行此脚本"
    exit 1
fi

# 安装依赖
echo "📦 检查并安装依赖..."
python3 -m pip install -r requirements.txt

# 初始化数据库
if [ ! -f "data/data.sqlite" ]; then
    echo "🗄️ 初始化数据库..."
    python3 create_admin.py
fi

# 启动服务
echo "🌟 启动服务..."
echo "访问地址: http://localhost:5000"
echo "管理后台: http://localhost:5000/admin"
echo "默认账号: admin / admin123"
echo "按 Ctrl+C 停止服务"
echo "=" * 50

python3 run.py
