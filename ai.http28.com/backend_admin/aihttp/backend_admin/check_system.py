#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""系统状态检查脚本"""
import os
import sys
import requests
import sqlite3
from datetime import datetime

def check_python_version():
    """检查Python版本"""
    print("🐍 Python版本检查...")
    version = sys.version_info
    if version >= (3, 7):
        print(f"✅ Python {version.major}.{version.minor}.{version.micro} (满足要求)")
        return True
    else:
        print(f"❌ Python {version.major}.{version.minor}.{version.micro} (需要3.7+)")
        return False

def check_dependencies():
    """检查依赖包"""
    print("\n📦 依赖包检查...")

    required_packages = [
        'flask', 'flask_sqlalchemy', 'flask_migrate',
        'flask_jwt_extended', 'flask_cors', 'werkzeug',
        'requests', 'python_dotenv', 'gunicorn'
    ]

    missing_packages = []

    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package} (未安装)")
            missing_packages.append(package)

    if missing_packages:
        print(f"\n⚠️ 缺少依赖包: {', '.join(missing_packages)}")
        print("请运行: pip install -r requirements.txt")
        return False

    return True

def check_database():
    """检查数据库"""
    print("\n🗄️ 数据库检查...")

    db_path = "data/data.sqlite"

    if not os.path.exists(db_path):
        print("❌ 数据库文件不存在")
        print("请运行: python create_admin.py")
        return False

    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # 检查表是否存在
        tables = ['users', 'software', 'software_versions', 'membership_plans', 'midjourney_tasks']

        for table in tables:
            cursor.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table}'")
            if cursor.fetchone():
                print(f"✅ 表 {table} 存在")
            else:
                print(f"❌ 表 {table} 不存在")
                return False

        # 检查管理员用户
        cursor.execute("SELECT COUNT(*) FROM users WHERE is_admin = 1")
        admin_count = cursor.fetchone()[0]

        if admin_count > 0:
            print(f"✅ 管理员用户存在 ({admin_count}个)")
        else:
            print("❌ 没有管理员用户")
            return False

        conn.close()
        return True

    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return False

def check_directories():
    """检查目录结构"""
    print("\n📁 目录结构检查...")

    required_dirs = [
        'app', 'app/routes', 'app/utils', 'data',
        'app/uploads', 'app/uploads/software'
    ]

    all_exist = True

    for directory in required_dirs:
        if os.path.exists(directory):
            print(f"✅ {directory}/")
        else:
            print(f"❌ {directory}/ (不存在)")
            all_exist = False

    return all_exist

def check_config_files():
    """检查配置文件"""
    print("\n⚙️ 配置文件检查...")

    required_files = [
        'config.py', 'requirements.txt', 'run.py',
        'create_admin.py', 'app/__init__.py', 'app/models.py'
    ]

    all_exist = True

    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path} (不存在)")
            all_exist = False

    return all_exist

def check_api_service():
    """检查API服务"""
    print("\n🌐 API服务检查...")

    try:
        response = requests.get("http://localhost:5000/", timeout=5)
        if response.status_code == 200:
            print("✅ API服务运行正常")

            # 测试登录接口
            login_data = {"username": "admin", "password": "admin123"}
            login_response = requests.post("http://localhost:5000/api/auth/login", json=login_data, timeout=5)

            if login_response.status_code == 200:
                print("✅ 登录接口正常")
                return True
            else:
                print(f"❌ 登录接口异常 (状态码: {login_response.status_code})")
                return False
        else:
            print(f"❌ API服务异常 (状态码: {response.status_code})")
            return False

    except requests.exceptions.ConnectionError:
        print("❌ API服务未启动")
        print("请运行: python run.py")
        return False
    except requests.exceptions.Timeout:
        print("❌ API服务响应超时")
        return False
    except Exception as e:
        print(f"❌ API服务检查失败: {e}")
        return False

def check_permissions():
    """检查文件权限"""
    print("\n🔒 文件权限检查...")

    critical_paths = ['data/', 'app/uploads/', 'logs/']

    all_ok = True

    for path in critical_paths:
        if os.path.exists(path):
            if os.access(path, os.R_OK | os.W_OK):
                print(f"✅ {path} (可读写)")
            else:
                print(f"❌ {path} (权限不足)")
                all_ok = False
        else:
            print(f"⚠️ {path} (不存在)")

    return all_ok

def get_system_info():
    """获取系统信息"""
    print("\n📊 系统信息...")

    try:
        from app import create_app, db
        from app.models import User, Software, MidjourneyTask

        app = create_app()
        with app.app_context():
            total_users = User.query.count()
            admin_users = User.query.filter_by(is_admin=True).count()
            paid_users = User.query.filter_by(is_paid_member=True).count()
            total_software = Software.query.count()
            total_tasks = MidjourneyTask.query.count()

            print(f"👥 总用户数: {total_users}")
            print(f"👑 管理员数: {admin_users}")
            print(f"💎 付费用户数: {paid_users}")
            print(f"💾 软件数量: {total_software}")
            print(f"🎨 AI任务数: {total_tasks}")

    except Exception as e:
        print(f"❌ 无法获取系统信息: {e}")

def main():
    """主检查函数"""
    print("🔍 XHS 后端管理系统状态检查")
    print("=" * 60)
    print(f"检查时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    checks = [
        ("Python版本", check_python_version),
        ("依赖包", check_dependencies),
        ("数据库", check_database),
        ("目录结构", check_directories),
        ("配置文件", check_config_files),
        ("文件权限", check_permissions),
        ("API服务", check_api_service),
    ]

    results = []

    for name, check_func in checks:
        try:
            result = check_func()
            results.append((name, result))
        except Exception as e:
            print(f"❌ {name}检查失败: {e}")
            results.append((name, False))

    # 获取系统信息
    get_system_info()

    # 总结
    print("\n" + "=" * 60)
    print("📋 检查结果总结:")

    passed = 0
    total = len(results)

    for name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {name}: {status}")
        if result:
            passed += 1

    print(f"\n🎯 总体状态: {passed}/{total} 项检查通过")

    if passed == total:
        print("🎉 系统状态良好，可以正常使用！")
        print("\n🌐 访问地址:")
        print("  主页: http://ai.http28.com:5000")
        print("  管理后台: http://ai.http28.com:5000/admin")
        print("  默认账号: admin / admin123")
    else:
        print("⚠️ 系统存在问题，请根据上述检查结果进行修复")
        print("\n🔧 常见解决方案:")
        print("  1. 安装依赖: pip install -r requirements.txt")
        print("  2. 初始化数据库: python create_admin.py")
        print("  3. 启动服务: python run.py")
        print("  4. 检查权限: chmod -R 755 data/ app/uploads/")

if __name__ == "__main__":
    main()
