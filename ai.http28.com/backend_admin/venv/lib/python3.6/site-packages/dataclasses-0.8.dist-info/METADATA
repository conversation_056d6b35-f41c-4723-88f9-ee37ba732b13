Metadata-Version: 2.1
Name: dataclasses
Version: 0.8
Summary: A backport of the dataclasses module for Python 3.6
Home-page: https://github.com/ericvsmith/dataclasses
Author: <PERSON>
Author-email: <EMAIL>
License: Apache
Platform: UNKNOWN
Classifier: Development Status :: 4 - Beta
Classifier: Intended Audience :: Developers
Classifier: Topic :: Software Development :: Libraries :: Python Modules
Classifier: License :: OSI Approved :: Apache Software License
Classifier: Programming Language :: Python :: 3.6
Requires-Python: >=3.6, <3.7

.. image:: https://img.shields.io/pypi/v/dataclasses.svg
   :target: https://pypi.org/project/dataclasses/


This is an implementation of PEP 557, Data Classes.  It is a backport
for Python 3.6.  Because dataclasses will be included in Python 3.7,
any discussion of dataclass features should occur on the python-dev
mailing list at https://mail.python.org/mailman/listinfo/python-dev.
At this point this repo should only be used for historical purposes
(it's where the original dataclasses discussions took place) and for
discussion of the actual backport to Python 3.6.

See https://www.python.org/dev/peps/pep-0557/ for the details of how
Data Classes work.

A test file can be found at
https://github.com/ericvsmith/dataclasses/blob/master/test/test_dataclasses.py,
or in the sdist file.

Installation
-------------

.. code-block::

  pip install dataclasses


Example Usage
-------------

.. code-block:: python

  from dataclasses import dataclass

  @dataclass
  class InventoryItem:
      name: str
      unit_price: float
      quantity_on_hand: int = 0

      def total_cost(self) -> float:
          return self.unit_price * self.quantity_on_hand

  item = InventoryItem('hammers', 10.49, 12)
  print(item.total_cost())

Some additional tools can be found in dataclass_tools.py, included in
the sdist.

Compatibility
-------------

This backport assumes that dict objects retain their insertion order.
This is true in the language spec for Python 3.7 and greater.  Since
this is a backport to Python 3.6, it raises an interesting question:
does that guarantee apply to 3.6?  For CPython 3.6 it does.  As of the
time of this writing, it's also true for all other Python
implementations that claim to be 3.6 compatible, of which there are
none.  Any new 3.6 implementations are expected to have ordered dicts.
See the analysis at the end of this email:

https://mail.python.org/pipermail/python-dev/2017-December/151325.html

As of version 0.4, this code no longer works with Python 3.7. For 3.7,
use the built-in dataclasses module.

Release History
---------------

+---------+------------+-------------------------------------+
| Version | Date       | Description                         |
+=========+============+=====================================+
| 0.8     | 2020-11-13 | Fix ClassVar in .replace()          |
+---------+------------+-------------------------------------+
| 0.7     | 2019-10-20 | Require python 3.6 only             |
+---------+------------+-------------------------------------+
| 0.6     | 2018-05-17 | Equivalent to Python 3.7.0rc1       |
+---------+------------+-------------------------------------+
| 0.5     | 2018-03-28 | Equivalent to Python 3.7.0b3        |
+---------+------------+-------------------------------------+


