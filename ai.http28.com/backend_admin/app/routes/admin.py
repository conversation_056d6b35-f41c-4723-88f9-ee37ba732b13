# -*- coding: utf-8 -*-
"""管理员相关路由"""
import os
import json
from datetime import datetime, timedelta
from flask import Blueprint, request, jsonify, current_app
from flask_jwt_extended import jwt_required, get_jwt_identity
from werkzeug.utils import secure_filename
from .. import db
from ..models import User, Software, SoftwareVersion, MembershipPlan, MidjourneyTask, ApiEndpoint, ApiCallLog
from ..utils.decorators import admin_required

admin_bp = Blueprint("admin", __name__)

# 用户管理
@admin_bp.route("/users", methods=["GET"])
@jwt_required()
@admin_required
def get_users():
    """获取用户列表"""
    page = request.args.get("page", 1, type=int)
    per_page = request.args.get("per_page", 10, type=int)
    search = request.args.get("search", "")

    # 构建查询
    query = User.query
    if search:
        query = query.filter(
            (User.username.ilike(f"%{search}%")) |
            (User.email.ilike(f"%{search}%"))
        )

    # 分页
    pagination = query.order_by(User.created_at.desc()).paginate(
        page=page, per_page=per_page, error_out=False
    )
    users = pagination.items

    return jsonify({
        "success": True,
        "users": [user.to_dict() for user in users],
        "pagination": {
            "page": page,
            "per_page": per_page,
            "total": pagination.total,
            "pages": pagination.pages
        }
    }), 200

@admin_bp.route("/users/<int:user_id>", methods=["GET"])
@jwt_required()
@admin_required
def get_user(user_id):
    """获取用户详情"""
    user = User.query.get(user_id)

    if not user:
        return jsonify({"success": False, "message": "用户不存在"}), 404

    return jsonify({
        "success": True,
        "user": user.to_dict()
    }), 200

@admin_bp.route("/users/<int:user_id>", methods=["PUT"])
@jwt_required()
@admin_required
def update_user(user_id):
    """更新用户信息"""
    user = User.query.get(user_id)

    if not user:
        return jsonify({"success": False, "message": "用户不存在"}), 404

    data = request.get_json()

    # 更新用户信息
    if 'email' in data:
        # 检查邮箱是否已存在
        existing_user = User.query.filter_by(email=data['email']).first()
        if existing_user and existing_user.id != user_id:
            return jsonify({"success": False, "message": "邮箱已存在"}), 400
        user.email = data['email']

    if 'is_admin' in data:
        user.is_admin = data['is_admin']

    if 'is_paid_member' in data:
        user.is_paid_member = data['is_paid_member']

    if 'membership_expiry' in data and data['membership_expiry']:
        try:
            user.membership_expiry = datetime.strptime(data['membership_expiry'], '%Y-%m-%d %H:%M:%S')
        except ValueError:
            return jsonify({"success": False, "message": "日期格式错误"}), 400

    if 'password' in data and data['password']:
        user.set_password(data['password'])

    db.session.commit()

    return jsonify({
        "success": True,
        "message": "用户信息已更新",
        "user": user.to_dict()
    }), 200

@admin_bp.route("/users", methods=["POST"])
@jwt_required()
@admin_required
def create_user():
    """创建新用户"""
    data = request.get_json()

    # 验证必填字段
    if not all(k in data for k in ["username", "email", "password"]):
        return jsonify({"success": False, "message": "缺少必填字段"}), 400

    # 检查用户名和邮箱是否已存在
    if User.query.filter_by(username=data["username"]).first():
        return jsonify({"success": False, "message": "用户名已存在"}), 400

    if User.query.filter_by(email=data["email"]).first():
        return jsonify({"success": False, "message": "邮箱已存在"}), 400

    # 创建新用户
    user = User(username=data["username"], email=data["email"])
    user.set_password(data["password"])

    if 'is_admin' in data:
        user.is_admin = data['is_admin']

    if 'is_paid_member' in data:
        user.is_paid_member = data['is_paid_member']

    if 'membership_expiry' in data and data['membership_expiry']:
        try:
            user.membership_expiry = datetime.strptime(data['membership_expiry'], '%Y-%m-%d %H:%M:%S')
        except ValueError:
            return jsonify({"success": False, "message": "日期格式错误"}), 400

    db.session.add(user)
    db.session.commit()

    return jsonify({
        "success": True,
        "message": "用户创建成功",
        "user": user.to_dict()
    }), 201

@admin_bp.route("/users/<int:user_id>", methods=["DELETE"])
@jwt_required()
@admin_required
def delete_user(user_id):
    """删除用户"""
    user = User.query.get(user_id)

    if not user:
        return jsonify({"success": False, "message": "用户不存在"}), 404

    # 防止删除管理员自己
    current_user_id = get_jwt_identity()
    if user_id == current_user_id:
        return jsonify({"success": False, "message": "不能删除自己"}), 400

    db.session.delete(user)
    db.session.commit()

    return jsonify({
        "success": True,
        "message": "用户已删除"
    }), 200

# 软件管理
@admin_bp.route("/software", methods=["GET"])
@jwt_required()
@admin_required
def get_software_list():
    """获取软件列表"""
    try:
        # 使用原始SQL查询避免SQLAlchemy模型问题
        from sqlalchemy import text
        result = db.session.execute(text("""
            SELECT id, name, description,
                   COALESCE(software_type, 'hybrid') as software_type,
                   COALESCE(category, 'api_tool') as category,
                   COALESCE(auth_required, 1) as auth_required,
                   COALESCE(membership_required, 0) as membership_required,
                   COALESCE(download_count, 0) as download_count,
                   COALESCE(api_call_count, 0) as api_call_count,
                   created_at, updated_at,
                   COALESCE(is_active, 1) as is_active
            FROM software
            ORDER BY created_at DESC
        """))

        software_data = []
        for row in result:
            software_data.append({
                "id": row[0],
                "name": row[1],
                "description": row[2] or "",
                "software_type": row[3] or "hybrid",
                "category": row[4] or "api_tool",
                "auth_required": bool(row[5]) if row[5] is not None else True,
                "membership_required": bool(row[6]) if row[6] is not None else False,
                "download_count": row[7] or 0,
                "api_call_count": row[8] or 0,
                "is_active": bool(row[11]) if row[11] is not None else True,
                "created_at": row[9] or "",
                "updated_at": row[10] or ""
            })

        return jsonify({
            "success": True,
            "software": software_data
        }), 200

    except Exception as e:
        return jsonify({
            "success": False,
            "message": f"获取软件列表失败: {str(e)}"
        }), 500

@admin_bp.route("/software", methods=["POST"])
@jwt_required()
@admin_required
def create_software():
    """创建新软件"""
    try:
        data = request.get_json()

        # 验证必填字段
        if not data.get("name"):
            return jsonify({"success": False, "message": "软件名称不能为空"}), 400

        # 使用原始SQL插入数据
        from sqlalchemy import text
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

        result = db.session.execute(text("""
            INSERT INTO software
            (name, description, software_type, category, auth_required,
             membership_required, download_count, api_call_count,
             created_at, updated_at, is_active)
            VALUES (:name, :description, :software_type, :category, :auth_required,
                    :membership_required, :download_count, :api_call_count,
                    :created_at, :updated_at, :is_active)
        """), {
            "name": data["name"],
            "description": data.get("description", ""),
            "software_type": data.get("software_type", "hybrid"),
            "category": data.get("category", "api_tool"),
            "auth_required": data.get("auth_required", True),
            "membership_required": data.get("membership_required", False),
            "download_count": 0,
            "api_call_count": 0,
            "created_at": current_time,
            "updated_at": current_time,
            "is_active": True
        })

        software_id = result.lastrowid
        db.session.commit()

        return jsonify({
            "success": True,
            "message": "软件创建成功",
            "software": {
                "id": software_id,
                "name": data["name"],
                "description": data.get("description", ""),
                "software_type": data.get("software_type", "hybrid"),
                "category": data.get("category", "api_tool"),
                "auth_required": data.get("auth_required", True),
                "membership_required": data.get("membership_required", False),
                "download_count": 0,
                "api_call_count": 0,
                "is_active": True,
                "created_at": current_time,
                "updated_at": current_time
            }
        }), 201

    except Exception as e:
        db.session.rollback()
        return jsonify({
            "success": False,
            "message": f"创建软件失败: {str(e)}"
        }), 500

@admin_bp.route("/software/<int:software_id>", methods=["PUT"])
@jwt_required()
@admin_required
def update_software(software_id):
    """更新软件信息"""
    software = Software.query.get(software_id)

    if not software:
        return jsonify({"success": False, "message": "软件不存在"}), 404

    data = request.get_json()

    if 'name' in data:
        software.name = data['name']
    if 'description' in data:
        software.description = data['description']
    if 'is_active' in data:
        software.is_active = data['is_active']

    db.session.commit()

    return jsonify({
        "success": True,
        "message": "软件信息已更新",
        "software": software.to_dict()
    }), 200

@admin_bp.route("/software/<int:software_id>", methods=["DELETE"])
@jwt_required()
@admin_required
def delete_software(software_id):
    """删除软件"""
    software = Software.query.get(software_id)

    if not software:
        return jsonify({"success": False, "message": "软件不存在"}), 404

    db.session.delete(software)
    db.session.commit()

    return jsonify({
        "success": True,
        "message": "软件已删除"
    }), 200

# 统计信息
@admin_bp.route("/stats", methods=["GET"])
@jwt_required()
@admin_required
def get_stats():
    """获取统计数据"""
    total_users = User.query.count()
    paid_users = User.query.filter_by(is_paid_member=True).count()

    # 计算活跃付费用户（会员未过期）
    active_paid_users = User.query.filter(
        User.is_paid_member == True,
        User.membership_expiry > datetime.now()
    ).count()

    total_software = Software.query.count()
    total_tasks = MidjourneyTask.query.count()

    # 最近7天的任务数
    week_ago = datetime.now() - timedelta(days=7)
    recent_tasks = MidjourneyTask.query.filter(
        MidjourneyTask.created_at >= week_ago
    ).count()

    return jsonify({
        "success": True,
        "stats": {
            "total_users": total_users,
            "paid_users": paid_users,
            "active_paid_users": active_paid_users,
            "total_software": total_software,
            "total_tasks": total_tasks,
            "recent_tasks": recent_tasks
        }
    }), 200

# 会员计划管理
@admin_bp.route("/membership-plans", methods=["GET"])
@jwt_required()
@admin_required
def get_membership_plans():
    """获取会员计划列表"""
    plans = MembershipPlan.query.order_by(MembershipPlan.created_at.desc()).all()

    return jsonify({
        "success": True,
        "plans": [plan.to_dict() for plan in plans]
    }), 200

# API端点管理
@admin_bp.route("/software/<int:software_id>/endpoints", methods=["GET"])
@jwt_required()
@admin_required
def get_software_endpoints(software_id):
    """获取软件的API端点列表"""
    software = Software.query.get(software_id)
    if not software:
        return jsonify({"success": False, "message": "软件不存在"}), 404

    endpoints = ApiEndpoint.query.filter_by(software_id=software_id).order_by(
        ApiEndpoint.created_at.desc()
    ).all()

    return jsonify({
        "success": True,
        "software": software.to_dict(),
        "endpoints": [ep.to_dict() for ep in endpoints]
    }), 200

@admin_bp.route("/software/<int:software_id>/endpoints", methods=["POST"])
@jwt_required()
@admin_required
def create_api_endpoint(software_id):
    """创建API端点"""
    software = Software.query.get(software_id)
    if not software:
        return jsonify({"success": False, "message": "软件不存在"}), 404

    data = request.get_json()

    # 验证必填字段
    required_fields = ["name", "endpoint_type", "target_url"]
    if not all(field in data for field in required_fields):
        return jsonify({"success": False, "message": "缺少必填字段"}), 400

    # 创建端点
    endpoint = ApiEndpoint(
        software_id=software_id,
        name=data["name"],
        description=data.get("description", ""),
        endpoint_type=data["endpoint_type"],
        target_url=data["target_url"],
        method=data.get("method", "POST"),
        auth_required=data.get("auth_required", True),
        membership_required=data.get("membership_required", False),
        rate_limit=data.get("rate_limit", 100)
    )

    # 设置配置
    if "headers_config" in data:
        endpoint.headers_config = json.dumps(data["headers_config"])
    if "params_config" in data:
        endpoint.params_config = json.dumps(data["params_config"])
    if "auth_config" in data:
        endpoint.auth_config = json.dumps(data["auth_config"])

    db.session.add(endpoint)
    db.session.commit()

    return jsonify({
        "success": True,
        "message": "API端点创建成功",
        "endpoint": endpoint.to_dict()
    }), 201

@admin_bp.route("/endpoints/<int:endpoint_id>", methods=["PUT"])
@jwt_required()
@admin_required
def update_api_endpoint(endpoint_id):
    """更新API端点"""
    endpoint = ApiEndpoint.query.get(endpoint_id)
    if not endpoint:
        return jsonify({"success": False, "message": "API端点不存在"}), 404

    data = request.get_json()

    # 更新字段
    if "name" in data:
        endpoint.name = data["name"]
    if "description" in data:
        endpoint.description = data["description"]
    if "endpoint_type" in data:
        endpoint.endpoint_type = data["endpoint_type"]
    if "target_url" in data:
        endpoint.target_url = data["target_url"]
    if "method" in data:
        endpoint.method = data["method"]
    if "auth_required" in data:
        endpoint.auth_required = data["auth_required"]
    if "membership_required" in data:
        endpoint.membership_required = data["membership_required"]
    if "rate_limit" in data:
        endpoint.rate_limit = data["rate_limit"]
    if "is_active" in data:
        endpoint.is_active = data["is_active"]

    # 更新配置
    if "headers_config" in data:
        endpoint.headers_config = json.dumps(data["headers_config"])
    if "params_config" in data:
        endpoint.params_config = json.dumps(data["params_config"])
    if "auth_config" in data:
        endpoint.auth_config = json.dumps(data["auth_config"])

    db.session.commit()

    return jsonify({
        "success": True,
        "message": "API端点已更新",
        "endpoint": endpoint.to_dict()
    }), 200

@admin_bp.route("/endpoints/<int:endpoint_id>", methods=["DELETE"])
@jwt_required()
@admin_required
def delete_api_endpoint(endpoint_id):
    """删除API端点"""
    endpoint = ApiEndpoint.query.get(endpoint_id)
    if not endpoint:
        return jsonify({"success": False, "message": "API端点不存在"}), 404

    db.session.delete(endpoint)
    db.session.commit()

    return jsonify({
        "success": True,
        "message": "API端点已删除"
    }), 200

# API调用日志管理
@admin_bp.route("/api-logs", methods=["GET"])
@jwt_required()
@admin_required
def get_api_logs():
    """获取API调用日志"""
    page = request.args.get("page", 1, type=int)
    per_page = request.args.get("per_page", 20, type=int)
    user_id = request.args.get("user_id", type=int)
    software_id = request.args.get("software_id", type=int)
    endpoint_id = request.args.get("endpoint_id", type=int)
    status = request.args.get("status")

    # 构建查询
    query = ApiCallLog.query

    if user_id:
        query = query.filter_by(user_id=user_id)
    if software_id:
        query = query.filter_by(software_id=software_id)
    if endpoint_id:
        query = query.filter_by(endpoint_id=endpoint_id)
    if status:
        query = query.filter_by(status=status)

    # 分页查询
    pagination = query.order_by(ApiCallLog.created_at.desc()).paginate(
        page=page, per_page=per_page, error_out=False
    )

    logs = pagination.items

    return jsonify({
        "success": True,
        "logs": [log.to_dict() for log in logs],
        "pagination": {
            "page": page,
            "per_page": per_page,
            "total": pagination.total,
            "pages": pagination.pages
        }
    }), 200

@admin_bp.route("/membership-plans", methods=["POST"])
@jwt_required()
@admin_required
def create_membership_plan():
    """创建会员计划"""
    data = request.get_json()

    # 验证必填字段
    if not all(k in data for k in ["name", "price", "duration_months"]):
        return jsonify({"success": False, "message": "缺少必填字段"}), 400

    # 检查计划名称是否已存在
    if MembershipPlan.query.filter_by(name=data["name"]).first():
        return jsonify({"success": False, "message": "计划名称已存在"}), 400

    # 创建会员计划
    plan = MembershipPlan(
        name=data["name"],
        description=data.get("description", ""),
        price=float(data["price"]),
        duration_months=int(data["duration_months"])
    )

    db.session.add(plan)
    db.session.commit()

    return jsonify({
        "success": True,
        "message": "会员计划创建成功",
        "plan": plan.to_dict()
    }), 201

@admin_bp.route("/membership-plans/<int:plan_id>", methods=["PUT"])
@jwt_required()
@admin_required
def update_membership_plan(plan_id):
    """更新会员计划"""
    plan = MembershipPlan.query.get(plan_id)

    if not plan:
        return jsonify({"success": False, "message": "会员计划不存在"}), 404

    data = request.get_json()

    if 'name' in data:
        # 检查名称是否已被其他计划使用
        existing_plan = MembershipPlan.query.filter_by(name=data['name']).first()
        if existing_plan and existing_plan.id != plan_id:
            return jsonify({"success": False, "message": "计划名称已存在"}), 400
        plan.name = data['name']

    if 'description' in data:
        plan.description = data['description']
    if 'price' in data:
        plan.price = float(data['price'])
    if 'duration_months' in data:
        plan.duration_months = int(data['duration_months'])
    if 'is_active' in data:
        plan.is_active = data['is_active']

    db.session.commit()

    return jsonify({
        "success": True,
        "message": "会员计划已更新",
        "plan": plan.to_dict()
    }), 200

@admin_bp.route("/membership-plans/<int:plan_id>", methods=["DELETE"])
@jwt_required()
@admin_required
def delete_membership_plan(plan_id):
    """删除会员计划"""
    plan = MembershipPlan.query.get(plan_id)

    if not plan:
        return jsonify({"success": False, "message": "会员计划不存在"}), 404

    db.session.delete(plan)
    db.session.commit()

    return jsonify({
        "success": True,
        "message": "会员计划已删除"
    }), 200
