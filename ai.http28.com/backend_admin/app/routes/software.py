# -*- coding: utf-8 -*-
"""软件相关路由"""
import os
from flask import Blueprint, request, jsonify, current_app, send_from_directory, redirect
from flask_jwt_extended import jwt_required, get_jwt_identity
from werkzeug.utils import secure_filename
from .. import db
from ..models import User, Software, SoftwareVersion
from ..utils.decorators import membership_required

software_bp = Blueprint("software", __name__)

@software_bp.route("/list", methods=["GET"])
@jwt_required()
def list_software():
    """获取软件列表"""
    user_id = get_jwt_identity()
    user = User.query.get(user_id)

    # 检查用户是否存在
    if not user:
        return jsonify({"success": False, "message": "用户不存在"}), 404

    # 获取软件列表
    software_list = Software.query.filter_by(is_active=True).all()

    return jsonify({
        "success": True,
        "software": [s.to_dict() for s in software_list],
        "user_membership_valid": user.is_membership_valid()
    }), 200

@software_bp.route("/<int:software_id>", methods=["GET"])
@jwt_required()
def get_software(software_id):
    """获取软件详情"""
    user_id = get_jwt_identity()
    user = User.query.get(user_id)

    # 检查用户是否存在
    if not user:
        return jsonify({"success": False, "message": "用户不存在"}), 404

    # 获取软件信息
    software = Software.query.get(software_id)

    if not software or not software.is_active:
        return jsonify({"success": False, "message": "软件不存在或已下架"}), 404

    # 获取软件版本列表
    versions = SoftwareVersion.query.filter_by(
        software_id=software_id, 
        is_active=True
    ).order_by(SoftwareVersion.created_at.desc()).all()

    return jsonify({
        "success": True,
        "software": software.to_dict(),
        "versions": [v.to_dict() for v in versions],
        "user_membership_valid": user.is_membership_valid()
    }), 200

@software_bp.route("/download/<int:version_id>", methods=["GET"])
@jwt_required()
@membership_required
def download_software(version_id):
    """下载软件"""
    user_id = get_jwt_identity()
    user = User.query.get(user_id)

    # 获取软件版本信息
    version = SoftwareVersion.query.get(version_id)

    if not version or not version.is_active:
        return jsonify({"success": False, "message": "软件版本不存在或已下架"}), 404

    # 获取软件信息
    software = Software.query.get(version.software_id)

    if not software or not software.is_active:
        return jsonify({"success": False, "message": "软件不存在或已下架"}), 404

    # 检查是否使用外部URL
    if version.is_external and version.external_url:
        # 返回重定向到外部URL
        return jsonify({
            "success": True,
            "is_external": True,
            "external_url": version.external_url,
            "message": "使用外部URL下载"
        })

    # 检查文件是否存在
    if not version.file_path or not os.path.exists(version.file_path):
        return jsonify({"success": False, "message": "文件不存在"}), 404

    # 发送文件
    file_path = version.file_path
    directory = os.path.dirname(file_path)
    filename = os.path.basename(file_path)

    try:
        return send_from_directory(directory, filename, as_attachment=True)
    except Exception as e:
        return jsonify({"success": False, "message": f"文件下载失败: {str(e)}"}), 500

@software_bp.route("/check-update/<string:software_name>/<string:current_version>", methods=["GET"])
@jwt_required()
@membership_required
def check_update(software_name, current_version):
    """检查软件更新"""
    user_id = get_jwt_identity()
    user = User.query.get(user_id)

    # 查找软件
    software = Software.query.filter_by(name=software_name, is_active=True).first()

    if not software:
        return jsonify({"success": False, "message": "软件不存在"}), 404

    # 获取最新版本
    latest_version = SoftwareVersion.query.filter_by(
        software_id=software.id,
        is_active=True
    ).order_by(SoftwareVersion.created_at.desc()).first()

    if not latest_version:
        return jsonify({"success": False, "message": "没有可用的版本"}), 404

    # 比较版本号
    # 这里使用简单的字符串比较，实际应用中可能需要更复杂的版本比较逻辑
    has_update = latest_version.version_number != current_version

    return jsonify({
        "success": True,
        "has_update": has_update,
        "current_version": current_version,
        "latest_version": latest_version.to_dict() if has_update else None,
        "download_url": f"/api/software/download/{latest_version.id}" if has_update else None
    }), 200
