# -*- coding: utf-8 -*-
"""API服务路由 - 统一的API中转和集成服务"""
import json
import time
import requests
from datetime import datetime, timedelta
from flask import Blueprint, request, jsonify, current_app
from flask_jwt_extended import jwt_required, get_jwt_identity
from .. import db
from ..models import User, Software, ApiEndpoint, ApiCallLog
from ..utils.decorators import membership_required

api_service_bp = Blueprint("api_service", __name__)

def check_rate_limit(user_id, endpoint_id, rate_limit):
    """检查API调用频率限制"""
    one_hour_ago = datetime.now() - timedelta(hours=1)
    recent_calls = ApiCallLog.query.filter(
        ApiCallLog.user_id == user_id,
        ApiCallLog.endpoint_id == endpoint_id,
        ApiCallLog.created_at >= one_hour_ago
    ).count()
    
    return recent_calls < rate_limit

def log_api_call(user_id, software_id, endpoint_id, request_data, response_data, response_time, status):
    """记录API调用日志"""
    try:
        log = ApiCallLog(
            user_id=user_id,
            software_id=software_id,
            endpoint_id=endpoint_id,
            request_method=request_data.get('method', 'POST'),
            request_url=request_data.get('url', ''),
            request_headers=json.dumps(request_data.get('headers', {})),
            request_body=json.dumps(request_data.get('body', {})),
            response_status=response_data.get('status_code', 0),
            response_headers=json.dumps(dict(response_data.get('headers', {}))),
            response_body=response_data.get('body', ''),
            response_time=response_time,
            status=status,
            error_message=response_data.get('error', '')
        )
        db.session.add(log)
        
        # 更新端点统计
        endpoint = ApiEndpoint.query.get(endpoint_id)
        if endpoint:
            endpoint.call_count += 1
            if status == 'success':
                endpoint.success_count += 1
            else:
                endpoint.error_count += 1
        
        # 更新软件统计
        software = Software.query.get(software_id)
        if software:
            software.api_call_count += 1
        
        db.session.commit()
    except Exception as e:
        current_app.logger.error(f"记录API调用日志失败: {e}")

@api_service_bp.route("/software/<int:software_id>/endpoints", methods=["GET"])
@jwt_required()
def get_software_endpoints(software_id):
    """获取软件的API端点列表"""
    user_id = get_jwt_identity()
    user = User.query.get(user_id)
    
    if not user:
        return jsonify({"success": False, "message": "用户不存在"}), 404
    
    # 获取软件信息
    software = Software.query.get(software_id)
    if not software or not software.is_active:
        return jsonify({"success": False, "message": "软件不存在或已下架"}), 404
    
    # 检查权限
    if software.auth_required and not user:
        return jsonify({"success": False, "message": "需要登录"}), 401
    
    if software.membership_required and not user.is_membership_valid():
        return jsonify({"success": False, "message": "需要有效会员"}), 403
    
    # 获取端点列表
    endpoints = ApiEndpoint.query.filter_by(
        software_id=software_id,
        is_active=True
    ).all()
    
    return jsonify({
        "success": True,
        "software": software.to_dict(),
        "endpoints": [ep.to_dict() for ep in endpoints]
    }), 200

@api_service_bp.route("/call/<int:endpoint_id>", methods=["POST"])
@jwt_required()
def call_api_endpoint(endpoint_id):
    """调用API端点"""
    user_id = get_jwt_identity()
    user = User.query.get(user_id)
    
    if not user:
        return jsonify({"success": False, "message": "用户不存在"}), 404
    
    # 获取端点信息
    endpoint = ApiEndpoint.query.get(endpoint_id)
    if not endpoint or not endpoint.is_active:
        return jsonify({"success": False, "message": "API端点不存在或已禁用"}), 404
    
    # 获取软件信息
    software = endpoint.software
    if not software or not software.is_active:
        return jsonify({"success": False, "message": "关联软件不存在或已下架"}), 404
    
    # 检查权限
    if endpoint.auth_required and not user:
        return jsonify({"success": False, "message": "需要登录"}), 401
    
    if endpoint.membership_required and not user.is_membership_valid():
        return jsonify({"success": False, "message": "需要有效会员"}), 403
    
    # 检查频率限制
    if not check_rate_limit(user_id, endpoint_id, endpoint.rate_limit):
        return jsonify({
            "success": False, 
            "message": f"API调用频率超限，每小时最多{endpoint.rate_limit}次"
        }), 429
    
    # 获取请求数据
    request_data = request.get_json() or {}
    
    # 构建API请求
    start_time = time.time()
    
    try:
        # 准备请求头
        headers = endpoint.get_headers_config()
        auth_config = endpoint.get_auth_config()
        
        # 处理认证
        if auth_config.get('type') == 'bearer':
            headers['Authorization'] = f"Bearer {auth_config.get('token', '')}"
        elif auth_config.get('type') == 'api_key':
            if auth_config.get('location') == 'header':
                headers[auth_config.get('key', 'X-API-Key')] = auth_config.get('value', '')
            elif auth_config.get('location') == 'query':
                request_data[auth_config.get('key', 'api_key')] = auth_config.get('value', '')
        
        # 合并参数配置
        params_config = endpoint.get_params_config()
        if params_config:
            request_data.update(params_config)
        
        # 发送请求
        response = requests.request(
            method=endpoint.method,
            url=endpoint.target_url,
            headers=headers,
            json=request_data if endpoint.method.upper() in ['POST', 'PUT', 'PATCH'] else None,
            params=request_data if endpoint.method.upper() == 'GET' else None,
            timeout=30
        )
        
        response_time = time.time() - start_time
        
        # 处理响应
        try:
            response_json = response.json()
        except:
            response_json = {"raw_response": response.text}
        
        # 记录日志
        log_api_call(
            user_id=user_id,
            software_id=software.id,
            endpoint_id=endpoint_id,
            request_data={
                'method': endpoint.method,
                'url': endpoint.target_url,
                'headers': headers,
                'body': request_data
            },
            response_data={
                'status_code': response.status_code,
                'headers': response.headers,
                'body': json.dumps(response_json)
            },
            response_time=response_time,
            status='success' if response.status_code < 400 else 'error'
        )
        
        return jsonify({
            "success": True,
            "endpoint": {
                "id": endpoint.id,
                "name": endpoint.name,
                "type": endpoint.endpoint_type
            },
            "response": {
                "status_code": response.status_code,
                "data": response_json,
                "response_time": round(response_time, 3)
            }
        }), response.status_code
        
    except requests.exceptions.Timeout:
        response_time = time.time() - start_time
        error_msg = "API请求超时"
        
        log_api_call(
            user_id=user_id,
            software_id=software.id,
            endpoint_id=endpoint_id,
            request_data={
                'method': endpoint.method,
                'url': endpoint.target_url,
                'headers': headers,
                'body': request_data
            },
            response_data={'error': error_msg},
            response_time=response_time,
            status='timeout'
        )
        
        return jsonify({
            "success": False,
            "message": error_msg,
            "response_time": round(response_time, 3)
        }), 408
        
    except Exception as e:
        response_time = time.time() - start_time
        error_msg = f"API调用失败: {str(e)}"
        
        log_api_call(
            user_id=user_id,
            software_id=software.id,
            endpoint_id=endpoint_id,
            request_data={
                'method': endpoint.method,
                'url': endpoint.target_url,
                'headers': headers,
                'body': request_data
            },
            response_data={'error': error_msg},
            response_time=response_time,
            status='error'
        )
        
        return jsonify({
            "success": False,
            "message": error_msg,
            "response_time": round(response_time, 3)
        }), 500

@api_service_bp.route("/logs", methods=["GET"])
@jwt_required()
def get_api_logs():
    """获取用户的API调用日志"""
    user_id = get_jwt_identity()
    
    # 获取分页参数
    page = request.args.get("page", 1, type=int)
    per_page = request.args.get("per_page", 20, type=int)
    software_id = request.args.get("software_id", type=int)
    endpoint_id = request.args.get("endpoint_id", type=int)
    status = request.args.get("status")
    
    # 构建查询
    query = ApiCallLog.query.filter_by(user_id=user_id)
    
    if software_id:
        query = query.filter_by(software_id=software_id)
    if endpoint_id:
        query = query.filter_by(endpoint_id=endpoint_id)
    if status:
        query = query.filter_by(status=status)
    
    # 分页查询
    pagination = query.order_by(ApiCallLog.created_at.desc()).paginate(
        page=page, per_page=per_page, error_out=False
    )
    
    logs = pagination.items
    
    return jsonify({
        "success": True,
        "logs": [log.to_dict() for log in logs],
        "pagination": {
            "page": page,
            "per_page": per_page,
            "total": pagination.total,
            "pages": pagination.pages
        }
    }), 200

@api_service_bp.route("/stats", methods=["GET"])
@jwt_required()
def get_api_stats():
    """获取用户的API使用统计"""
    user_id = get_jwt_identity()
    
    # 总调用次数
    total_calls = ApiCallLog.query.filter_by(user_id=user_id).count()
    
    # 成功调用次数
    success_calls = ApiCallLog.query.filter_by(user_id=user_id, status='success').count()
    
    # 今日调用次数
    today = datetime.now().date()
    today_calls = ApiCallLog.query.filter(
        ApiCallLog.user_id == user_id,
        ApiCallLog.created_at >= today
    ).count()
    
    # 最近7天的调用统计
    week_ago = datetime.now() - timedelta(days=7)
    recent_calls = ApiCallLog.query.filter(
        ApiCallLog.user_id == user_id,
        ApiCallLog.created_at >= week_ago
    ).count()
    
    # 按软件统计
    software_stats = db.session.query(
        Software.name,
        db.func.count(ApiCallLog.id).label('call_count')
    ).join(ApiCallLog).filter(
        ApiCallLog.user_id == user_id
    ).group_by(Software.id).all()
    
    return jsonify({
        "success": True,
        "stats": {
            "total_calls": total_calls,
            "success_calls": success_calls,
            "success_rate": round(success_calls / max(total_calls, 1) * 100, 2),
            "today_calls": today_calls,
            "recent_calls": recent_calls,
            "software_stats": [
                {"software": name, "calls": count} 
                for name, count in software_stats
            ]
        }
    }), 200
