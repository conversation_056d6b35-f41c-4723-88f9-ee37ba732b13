# -*- coding: utf-8 -*-
"""Midjourney相关路由"""
import json
import requests
from datetime import datetime
from flask import Blueprint, request, jsonify, current_app
from flask_jwt_extended import jwt_required, get_jwt_identity
from .. import db
from ..models import User, MidjourneyTask
from ..utils.decorators import membership_required

midjourney_bp = Blueprint("midjourney", __name__)

def call_midjourney_api(endpoint, data=None, method="GET"):
    """调用Midjourney API"""
    api_key = current_app.config["MIDJOURNEY_API_KEY"]
    api_base = current_app.config["MIDJOURNEY_API_BASE"]
    url = f"{api_base}/{endpoint}"
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {api_key}"
    }

    try:
        if method == "GET":
            response = requests.get(url, headers=headers, timeout=30)
        else:
            response = requests.post(url, headers=headers, json=data, timeout=30)

        # 检查响应状态
        if response.status_code == 200:
            return response.json()
        else:
            error_msg = f"API错误: {response.status_code}"
            try:
                error_data = response.json()
                return {"error": error_data.get("message", error_msg)}
            except:
                return {"error": error_msg}
    except requests.exceptions.Timeout:
        return {"error": "API请求超时"}
    except requests.exceptions.ConnectionError:
        return {"error": "API连接失败"}
    except Exception as e:
        return {"error": str(e)}

@midjourney_bp.route("/generate", methods=["POST"])
@jwt_required()
@membership_required
def generate_image():
    """生成Midjourney图像"""
    user_id = get_jwt_identity()
    user = User.query.get(user_id)

    # 获取请求数据
    data = request.get_json()

    # 验证必填字段
    if "prompt" not in data:
        return jsonify({"success": False, "message": "缺少提示词"}), 400

    prompt = data["prompt"]

    # 调用Midjourney API
    api_data = {
        "prompt": prompt,
        "model": data.get("model", "midjourney"),
        "aspect_ratio": data.get("aspect_ratio", "1:1")
    }

    response = call_midjourney_api("imagine", api_data, "POST")

    # 检查API响应
    if "error" in response:
        return jsonify({"success": False, "message": response["error"]}), 400

    # 保存任务到数据库
    task_id = response.get("task_id", f"task_{user_id}_{int(datetime.now().timestamp())}")
    task = MidjourneyTask(
        user_id=user_id,
        task_id=task_id,
        prompt=prompt,
        status="pending",
        raw_response=json.dumps(response)
    )

    db.session.add(task)
    db.session.commit()

    return jsonify({
        "success": True,
        "message": "任务已提交",
        "task": task.to_dict()
    }), 201

@midjourney_bp.route("/tasks/<int:task_id>", methods=["GET"])
@jwt_required()
def get_task(task_id):
    """获取任务状态"""
    user_id = get_jwt_identity()

    # 查找任务
    task = MidjourneyTask.query.get(task_id)

    if not task:
        return jsonify({"success": False, "message": "任务不存在"}), 404

    # 验证用户权限
    if task.user_id != user_id:
        return jsonify({"success": False, "message": "无权访问此任务"}), 403

    # 如果任务状态是pending，则查询API获取最新状态
    if task.status == "pending":
        try:
            response = call_midjourney_api(f"tasks/{task.task_id}")

            if "error" not in response:
                # 更新任务状态
                api_status = response.get("status")
                if api_status == "completed":
                    task.status = "completed"
                    task.result_url = response.get("image_url")
                elif api_status == "failed":
                    task.status = "failed"
                    task.error_message = response.get("error", "任务失败")

                # 更新原始响应
                task.raw_response = json.dumps(response)
                db.session.commit()
        except Exception as e:
            # 如果API调用失败，继续使用数据库中的状态
            pass

    return jsonify({
        "success": True,
        "task": task.to_dict()
    }), 200

@midjourney_bp.route("/tasks", methods=["GET"])
@jwt_required()
def get_user_tasks():
    """获取用户所有任务"""
    user_id = get_jwt_identity()

    # 获取分页参数
    page = request.args.get("page", 1, type=int)
    per_page = request.args.get("per_page", 10, type=int)

    # 查询用户任务
    pagination = MidjourneyTask.query.filter_by(user_id=user_id).order_by(
        MidjourneyTask.created_at.desc()
    ).paginate(page=page, per_page=per_page, error_out=False)

    tasks = pagination.items

    return jsonify({
        "success": True,
        "tasks": [task.to_dict() for task in tasks],
        "pagination": {
            "page": page,
            "per_page": per_page,
            "total": pagination.total,
            "pages": pagination.pages
        }
    }), 200
