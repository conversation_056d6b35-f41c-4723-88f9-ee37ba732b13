# -*- coding: utf-8 -*-
"""认证相关路由"""
from datetime import datetime, timedelta
from flask import Blueprint, request, jsonify, current_app
from flask_jwt_extended import create_access_token, create_refresh_token, jwt_required, get_jwt_identity
from .. import db
from ..models import User, MembershipPlan, Order

auth_bp = Blueprint('auth', __name__)

@auth_bp.route('/register', methods=['POST'])
def register():
    """用户注册"""
    data = request.get_json()
    
    # 验证必填字段
    if not all(k in data for k in ["username", "email", "password"]):
        return jsonify({"success": False, "message": "缺少必填字段"}), 400
    
    # 检查用户名和邮箱是否已存在
    if User.query.filter_by(username=data["username"]).first():
        return jsonify({"success": False, "message": "用户名已存在"}), 400
    
    if User.query.filter_by(email=data["email"]).first():
        return jsonify({"success": False, "message": "邮箱已存在"}), 400
    
    # 创建新用户
    user = User(username=data["username"], email=data["email"])
    user.set_password(data["password"])
    
    db.session.add(user)
    db.session.commit()
    
    # 生成访问令牌
    access_token = create_access_token(identity=user.id)
    refresh_token = create_refresh_token(identity=user.id)
    
    return jsonify({
        "success": True,
        "message": "注册成功",
        "access_token": access_token,
        "refresh_token": refresh_token,
        "user": user.to_dict()
    }), 201

@auth_bp.route('/login', methods=['POST'])
def login():
    """用户登录"""
    data = request.get_json()
    
    # 验证必填字段
    if not all(k in data for k in ["username", "password"]):
        return jsonify({"success": False, "message": "缺少必填字段"}), 400
    
    # 查找用户
    user = User.query.filter_by(username=data["username"]).first()
    
    # 验证用户和密码
    if not user or not user.check_password(data["password"]):
        return jsonify({"success": False, "message": "用户名或密码错误"}), 401
    
    # 更新最后登录时间
    user.last_login = datetime.now()
    db.session.commit()
    
    # 生成访问令牌
    access_token = create_access_token(identity=user.id)
    refresh_token = create_refresh_token(identity=user.id)
    
    return jsonify({
        "success": True,
        "message": "登录成功",
        "access_token": access_token,
        "refresh_token": refresh_token,
        "user": user.to_dict()
    }), 200

@auth_bp.route('/refresh', methods=['POST'])
@jwt_required(refresh=True)
def refresh():
    """刷新访问令牌"""
    user_id = get_jwt_identity()
    user = User.query.get(user_id)
    
    if not user:
        return jsonify({"success": False, "message": "用户不存在"}), 401
    
    access_token = create_access_token(identity=user_id)
    
    return jsonify({
        "success": True,
        "access_token": access_token
    }), 200

@auth_bp.route('/profile', methods=['GET'])
@jwt_required()
def get_profile():
    """获取用户资料"""
    user_id = get_jwt_identity()
    user = User.query.get(user_id)
    
    if not user:
        return jsonify({"success": False, "message": "用户不存在"}), 404
    
    return jsonify({
        "success": True,
        "user": user.to_dict()
    }), 200

@auth_bp.route('/profile', methods=['PUT'])
@jwt_required()
def update_profile():
    """更新用户资料"""
    user_id = get_jwt_identity()
    user = User.query.get(user_id)
    
    if not user:
        return jsonify({"success": False, "message": "用户不存在"}), 404
    
    data = request.get_json()
    
    # 更新邮箱
    if 'email' in data and data['email'] != user.email:
        if User.query.filter_by(email=data['email']).first():
            return jsonify({"success": False, "message": "邮箱已存在"}), 400
        user.email = data['email']
    
    # 更新密码
    if 'password' in data and data['password']:
        user.set_password(data['password'])
    
    db.session.commit()
    
    return jsonify({
        "success": True,
        "message": "个人资料已更新",
        "user": user.to_dict()
    }), 200
