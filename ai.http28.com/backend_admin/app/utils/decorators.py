# -*- coding: utf-8 -*-
"""装饰器工具"""
from functools import wraps
from flask import jsonify
from flask_jwt_extended import get_jwt_identity
from ..models import User

def admin_required(f):
    """管理员权限装饰器"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        user_id = get_jwt_identity()
        user = User.query.get(user_id)
        
        if not user or not user.is_admin:
            return jsonify({"success": False, "message": "需要管理员权限"}), 403
        
        return f(*args, **kwargs)
    return decorated_function

def membership_required(f):
    """会员权限装饰器"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        user_id = get_jwt_identity()
        user = User.query.get(user_id)
        
        if not user or not user.is_membership_valid():
            return jsonify({"success": False, "message": "需要有效的会员资格"}), 403
        
        return f(*args, **kwargs)
    return decorated_function
