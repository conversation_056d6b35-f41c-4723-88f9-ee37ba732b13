#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接测试数据库查询
"""
import sqlite3
import json

def test_database():
    """测试数据库查询"""
    try:
        # 连接数据库
        conn = sqlite3.connect('/www/wwwroot/ai.http28.com/backend_admin/data/data.sqlite')
        cursor = conn.cursor()
        
        # 查看表结构
        print("=== 软件表结构 ===")
        cursor.execute("PRAGMA table_info(software)")
        columns = cursor.fetchall()
        for col in columns:
            print(f"{col[1]} ({col[2]})")
        
        # 查询所有软件
        print("\n=== 软件列表 ===")
        cursor.execute("SELECT * FROM software")
        rows = cursor.fetchall()
        
        for row in rows:
            print(f"ID: {row[0]}, Name: {row[1]}")
        
        # 测试新字段查询
        print("\n=== 测试新字段查询 ===")
        try:
            cursor.execute("""
                SELECT id, name, description, software_type, category
                FROM software 
                LIMIT 1
            """)
            row = cursor.fetchone()
            if row:
                print(f"查询成功: {row}")
            else:
                print("没有数据")
        except Exception as e:
            print(f"查询失败: {e}")
        
        conn.close()
        
    except Exception as e:
        print(f"数据库连接失败: {e}")

if __name__ == "__main__":
    test_database()
