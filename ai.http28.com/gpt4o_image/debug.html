<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GPT-4o 调试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f0f0f0;
        }
        .debug-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 5px 0;
            border-radius: 5px;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        button {
            background: #4ade80;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #22c55e;
        }
        #console {
            background: #000;
            color: #0f0;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            height: 200px;
            overflow-y: auto;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="debug-container">
        <h1>GPT-4o Image VIP 调试工具</h1>
        
        <div id="status"></div>
        
        <button onclick="checkElements()">检查DOM元素</button>
        <button onclick="testUpload()">测试上传功能</button>
        <button onclick="clearConsole()">清空控制台</button>
        <button onclick="window.open('index.html', '_blank')">打开主应用</button>
        
        <div id="console"></div>
        
        <input type="file" id="testFileInput" accept="image/*" style="display: none;">
        <div id="testPreview" style="display: none;">
            <img id="testImage" style="max-width: 200px; border: 1px solid #ddd;">
        </div>
    </div>

    <script>
        const consoleDiv = document.getElementById('console');
        const statusDiv = document.getElementById('status');
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}`;
            consoleDiv.innerHTML += logEntry + '<br>';
            consoleDiv.scrollTop = consoleDiv.scrollHeight;
            
            if (type === 'error') {
                console.error(message);
            } else {
                console.log(message);
            }
        }
        
        function clearConsole() {
            consoleDiv.innerHTML = '';
        }
        
        function checkElements() {
            log('开始检查DOM元素...');
            
            // 检查主应用的关键元素
            const elements = [
                'loginPage', 'mainApp', 'uploadBtn', 'imageUpload', 
                'imagePreview', 'previewImage', 'removeImageBtn'
            ];
            
            let allFound = true;
            elements.forEach(id => {
                const element = document.getElementById(id);
                if (element) {
                    log(`✅ ${id}: 找到`, 'success');
                } else {
                    log(`❌ ${id}: 未找到`, 'error');
                    allFound = false;
                }
            });
            
            if (allFound) {
                statusDiv.innerHTML = '<div class="status success">所有关键元素都已找到</div>';
            } else {
                statusDiv.innerHTML = '<div class="status error">部分元素缺失，请检查HTML结构</div>';
            }
        }
        
        function testUpload() {
            log('测试上传功能...');
            
            const fileInput = document.getElementById('testFileInput');
            const testImage = document.getElementById('testImage');
            const testPreview = document.getElementById('testPreview');
            
            fileInput.addEventListener('change', function(e) {
                const file = e.target.files[0];
                if (!file) {
                    log('没有选择文件', 'error');
                    return;
                }
                
                log(`文件选择成功: ${file.name} (${(file.size/1024/1024).toFixed(2)}MB)`);
                
                if (!file.type.startsWith('image/')) {
                    log('文件类型错误: ' + file.type, 'error');
                    return;
                }
                
                const reader = new FileReader();
                reader.onload = function(e) {
                    testImage.src = e.target.result;
                    testPreview.style.display = 'block';
                    log('图片预览显示成功');
                    statusDiv.innerHTML = '<div class="status success">上传功能测试通过</div>';
                };
                reader.onerror = function() {
                    log('图片读取失败', 'error');
                    statusDiv.innerHTML = '<div class="status error">图片读取失败</div>';
                };
                reader.readAsDataURL(file);
            });
            
            fileInput.click();
        }
        
        // 页面加载时自动检查
        window.addEventListener('load', () => {
            log('调试页面加载完成');
            log('浏览器: ' + navigator.userAgent);
            log('当前URL: ' + window.location.href);
            
            // 检查是否可以访问主应用
            fetch('index.html')
                .then(response => {
                    if (response.ok) {
                        log('✅ 主应用文件可访问');
                    } else {
                        log('❌ 主应用文件访问失败: ' + response.status, 'error');
                    }
                })
                .catch(error => {
                    log('❌ 网络错误: ' + error.message, 'error');
                });
        });
    </script>
</body>
</html>
