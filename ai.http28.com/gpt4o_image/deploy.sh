#!/bin/bash

# GPT-4o Image VIP 前端部署脚本
# 作者: AI Assistant
# 日期: 2025-05-25

echo "🚀 开始部署 GPT-4o Image VIP 前端应用..."

# 设置变量
FRONTEND_DIR="/www/wwwroot/gpt4o_image"
NGINX_CONF="/etc/nginx/sites-available/gpt4o_image"
DOMAIN="gpt4o.ai.http28.com"

# 创建部署目录
echo "📁 创建部署目录..."
sudo mkdir -p $FRONTEND_DIR
sudo chown -R www-data:www-data $FRONTEND_DIR

# 复制前端文件
echo "📋 复制前端文件..."
sudo cp index.html $FRONTEND_DIR/
sudo cp styles.css $FRONTEND_DIR/
sudo cp script.js $FRONTEND_DIR/
sudo cp README.md $FRONTEND_DIR/

# 设置文件权限
echo "🔐 设置文件权限..."
sudo chmod 644 $FRONTEND_DIR/*
sudo chown -R www-data:www-data $FRONTEND_DIR

# 创建Nginx配置
echo "⚙️ 创建Nginx配置..."
sudo tee $NGINX_CONF > /dev/null <<EOF
server {
    listen 80;
    server_name $DOMAIN;
    
    root $FRONTEND_DIR;
    index index.html;
    
    # 启用gzip压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;
    
    # 静态文件缓存
    location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # 主页面
    location / {
        try_files \$uri \$uri/ /index.html;
        add_header X-Frame-Options "SAMEORIGIN" always;
        add_header X-Content-Type-Options "nosniff" always;
        add_header X-XSS-Protection "1; mode=block" always;
    }
    
    # API代理（如果需要）
    location /api/ {
        proxy_pass http://************:5000;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }
    
    # 错误页面
    error_page 404 /index.html;
    error_page 500 502 503 504 /index.html;
}
EOF

# 启用站点
echo "🔗 启用Nginx站点..."
sudo ln -sf $NGINX_CONF /etc/nginx/sites-enabled/
sudo nginx -t

if [ $? -eq 0 ]; then
    echo "✅ Nginx配置测试通过"
    sudo systemctl reload nginx
    echo "🔄 Nginx已重新加载"
else
    echo "❌ Nginx配置测试失败"
    exit 1
fi

# 创建SSL证书（可选）
echo "🔒 设置SSL证书..."
if command -v certbot &> /dev/null; then
    sudo certbot --nginx -d $DOMAIN --non-interactive --agree-tos --email <EMAIL>
    echo "✅ SSL证书已配置"
else
    echo "⚠️ Certbot未安装，跳过SSL配置"
fi

# 创建日志目录
echo "📝 创建日志目录..."
sudo mkdir -p /var/log/nginx/gpt4o_image
sudo chown -R www-data:www-data /var/log/nginx/gpt4o_image

# 创建备份脚本
echo "💾 创建备份脚本..."
sudo tee /usr/local/bin/backup_gpt4o_frontend.sh > /dev/null <<EOF
#!/bin/bash
BACKUP_DIR="/backup/gpt4o_frontend"
DATE=\$(date +%Y%m%d_%H%M%S)

mkdir -p \$BACKUP_DIR
tar -czf \$BACKUP_DIR/gpt4o_frontend_\$DATE.tar.gz -C $FRONTEND_DIR .
find \$BACKUP_DIR -name "*.tar.gz" -mtime +7 -delete

echo "备份完成: gpt4o_frontend_\$DATE.tar.gz"
EOF

sudo chmod +x /usr/local/bin/backup_gpt4o_frontend.sh

# 添加到crontab（每天备份）
echo "⏰ 设置自动备份..."
(crontab -l 2>/dev/null; echo "0 2 * * * /usr/local/bin/backup_gpt4o_frontend.sh") | crontab -

# 创建监控脚本
echo "📊 创建监控脚本..."
sudo tee /usr/local/bin/monitor_gpt4o_frontend.sh > /dev/null <<EOF
#!/bin/bash
URL="http://$DOMAIN"
STATUS=\$(curl -s -o /dev/null -w "%{http_code}" \$URL)

if [ \$STATUS -eq 200 ]; then
    echo "\$(date): GPT-4o Frontend is running (HTTP \$STATUS)"
else
    echo "\$(date): GPT-4o Frontend is down (HTTP \$STATUS)" | tee -a /var/log/gpt4o_frontend_monitor.log
    # 可以在这里添加告警通知
fi
EOF

sudo chmod +x /usr/local/bin/monitor_gpt4o_frontend.sh

# 添加监控到crontab（每5分钟检查）
echo "🔍 设置健康检查..."
(crontab -l 2>/dev/null; echo "*/5 * * * * /usr/local/bin/monitor_gpt4o_frontend.sh") | crontab -

# 创建更新脚本
echo "🔄 创建更新脚本..."
sudo tee /usr/local/bin/update_gpt4o_frontend.sh > /dev/null <<EOF
#!/bin/bash
echo "更新 GPT-4o Image VIP 前端应用..."

# 备份当前版本
/usr/local/bin/backup_gpt4o_frontend.sh

# 从源目录复制新文件
cp /www/wwwroot/aihttp/gpt4o_image_frontend/* $FRONTEND_DIR/

# 设置权限
chown -R www-data:www-data $FRONTEND_DIR
chmod 644 $FRONTEND_DIR/*

# 重新加载Nginx
nginx -t && systemctl reload nginx

echo "更新完成！"
EOF

sudo chmod +x /usr/local/bin/update_gpt4o_frontend.sh

# 显示部署信息
echo ""
echo "🎉 GPT-4o Image VIP 前端应用部署完成！"
echo ""
echo "📋 部署信息:"
echo "   域名: $DOMAIN"
echo "   目录: $FRONTEND_DIR"
echo "   配置: $NGINX_CONF"
echo ""
echo "🔧 管理命令:"
echo "   更新应用: sudo /usr/local/bin/update_gpt4o_frontend.sh"
echo "   手动备份: sudo /usr/local/bin/backup_gpt4o_frontend.sh"
echo "   健康检查: sudo /usr/local/bin/monitor_gpt4o_frontend.sh"
echo ""
echo "📊 访问地址:"
echo "   HTTP: http://$DOMAIN"
if command -v certbot &> /dev/null; then
    echo "   HTTPS: https://$DOMAIN"
fi
echo ""
echo "✅ 部署成功！请在浏览器中访问应用。"
