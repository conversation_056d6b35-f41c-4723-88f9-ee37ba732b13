/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
    color: #ffffff;
    overflow: hidden;
}

/* 登录页面样式 */
.login-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
}

.login-box {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 40px;
    width: 400px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.login-header {
    text-align: center;
    margin-bottom: 30px;
}

.logo {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
    margin-bottom: 10px;
}

.logo i {
    font-size: 2.5rem;
    color: #4ade80;
}

.logo h1 {
    font-size: 1.8rem;
    font-weight: 600;
    color: #ffffff;
}

.subtitle {
    color: #cbd5e1;
    font-size: 0.9rem;
}

.login-form {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.form-group label {
    font-size: 0.9rem;
    color: #e2e8f0;
    font-weight: 500;
}

.form-group input {
    padding: 12px 16px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 10px;
    background: rgba(255, 255, 255, 0.1);
    color: #ffffff;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.form-group input:focus {
    outline: none;
    border-color: #4ade80;
    box-shadow: 0 0 0 3px rgba(74, 222, 128, 0.2);
}

.form-group input::placeholder {
    color: #94a3b8;
}

.login-btn {
    padding: 14px 20px;
    background: linear-gradient(135deg, #4ade80 0%, #22c55e 100%);
    border: none;
    border-radius: 10px;
    color: #ffffff;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.login-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(74, 222, 128, 0.3);
}

.error-message {
    color: #ef4444;
    font-size: 0.9rem;
    text-align: center;
    padding: 10px;
    background: rgba(239, 68, 68, 0.1);
    border-radius: 8px;
    border: 1px solid rgba(239, 68, 68, 0.3);
}

/* 主应用样式 */
.app-container {
    display: flex;
    height: 100vh;
    background: #1e293b;
}

/* 侧边栏样式 */
.sidebar {
    width: 280px;
    background: #0f172a;
    display: flex;
    flex-direction: column;
    border-right: 1px solid #334155;
}

.sidebar-header {
    padding: 20px;
    border-bottom: 1px solid #334155;
    display: flex;
    align-items: center;
    gap: 15px;
}

.server-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #4ade80 0%, #22c55e 100%);
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: #ffffff;
}

.sidebar-header h3 {
    font-size: 1.2rem;
    font-weight: 600;
    color: #ffffff;
}

.channel-list {
    flex: 1;
    padding: 20px 0;
    overflow-y: auto;
}

.channel-category {
    padding: 0 20px 10px;
    margin-bottom: 10px;
}

.channel-category h4 {
    font-size: 0.8rem;
    color: #64748b;
    text-transform: uppercase;
    font-weight: 600;
    letter-spacing: 0.5px;
}

.channel-item {
    padding: 10px 20px;
    display: flex;
    align-items: center;
    gap: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
    color: #94a3b8;
    margin: 2px 10px;
    border-radius: 8px;
}

.channel-item:hover {
    background: rgba(74, 222, 128, 0.1);
    color: #4ade80;
}

.channel-item.active {
    background: rgba(74, 222, 128, 0.2);
    color: #4ade80;
}

.channel-item i {
    font-size: 1rem;
    width: 20px;
}

.user-info {
    padding: 15px 20px;
    border-top: 1px solid #334155;
    display: flex;
    align-items: center;
    gap: 12px;
}

.user-avatar {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #4ade80 0%, #22c55e 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    color: #ffffff;
}

.user-details {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.username {
    font-size: 0.9rem;
    font-weight: 600;
    color: #ffffff;
}

.status {
    font-size: 0.8rem;
    color: #4ade80;
}

.logout-btn {
    background: none;
    border: none;
    color: #64748b;
    cursor: pointer;
    padding: 8px;
    border-radius: 6px;
    transition: all 0.2s ease;
}

.logout-btn:hover {
    background: rgba(239, 68, 68, 0.1);
    color: #ef4444;
}

/* 主内容区域 */
.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: #1e293b;
}

.content-header {
    padding: 15px 25px;
    border-bottom: 1px solid #334155;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: #0f172a;
}

.channel-info {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 1.1rem;
    font-weight: 600;
    color: #ffffff;
}

.channel-info i {
    color: #64748b;
}

.header-actions {
    display: flex;
    gap: 10px;
}

.action-btn {
    background: none;
    border: none;
    color: #64748b;
    cursor: pointer;
    padding: 8px;
    border-radius: 6px;
    transition: all 0.2s ease;
    font-size: 1rem;
}

.action-btn:hover {
    background: rgba(74, 222, 128, 0.1);
    color: #4ade80;
}

/* 消息容器 */
.messages-container {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
}

.welcome-message {
    text-align: center;
    margin: auto;
    max-width: 500px;
}

.welcome-icon {
    font-size: 4rem;
    color: #4ade80;
    margin-bottom: 20px;
}

.welcome-message h2 {
    font-size: 1.8rem;
    margin-bottom: 10px;
    color: #ffffff;
}

.welcome-message p {
    color: #94a3b8;
    font-size: 1rem;
    line-height: 1.6;
}

/* 输入区域 */
.input-area {
    padding: 20px;
    border-top: 1px solid #334155;
    background: #0f172a;
}

.message-form {
    max-width: 100%;
}

.input-container {
    display: flex;
    align-items: flex-end;
    gap: 15px;
    background: #1e293b;
    border-radius: 15px;
    padding: 15px;
    border: 1px solid #334155;
}

#promptInput {
    flex: 1;
    background: none;
    border: none;
    color: #ffffff;
    font-size: 1rem;
    resize: none;
    outline: none;
    min-height: 24px;
    max-height: 120px;
    line-height: 1.5;
}

#promptInput::placeholder {
    color: #64748b;
}

.input-actions {
    display: flex;
    gap: 10px;
    align-items: center;
}

.send-btn {
    background: linear-gradient(135deg, #4ade80 0%, #22c55e 100%);
    border: none;
    border-radius: 10px;
    color: #ffffff;
    cursor: pointer;
    padding: 10px 15px;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.send-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(74, 222, 128, 0.3);
}

.send-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* 设置面板 */
.settings-panel {
    margin-top: 15px;
    padding: 20px;
    background: #1e293b;
    border-radius: 10px;
    border: 1px solid #334155;
}

.settings-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 15px;
}

.settings-row:last-child {
    margin-bottom: 0;
}

.settings-row label {
    color: #e2e8f0;
    font-size: 0.9rem;
    font-weight: 500;
}

.settings-row select {
    background: #0f172a;
    border: 1px solid #334155;
    border-radius: 8px;
    color: #ffffff;
    padding: 8px 12px;
    font-size: 0.9rem;
    cursor: pointer;
}

.settings-row select:focus {
    outline: none;
    border-color: #4ade80;
}

/* 加载动画 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.loading-spinner {
    text-align: center;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 4px solid #334155;
    border-top: 4px solid #4ade80;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-spinner p {
    color: #ffffff;
    font-size: 1rem;
}

/* 消息气泡样式 */
.message-bubble {
    margin-bottom: 20px;
    display: flex;
    flex-direction: column;
    max-width: 80%;
}

.message-bubble.user {
    align-self: flex-end;
    align-items: flex-end;
}

.message-bubble.ai {
    align-self: flex-start;
    align-items: flex-start;
}

.message-header {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 8px;
}

.message-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.9rem;
    color: #ffffff;
}

.message-bubble.user .message-avatar {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
}

.message-bubble.ai .message-avatar {
    background: linear-gradient(135deg, #4ade80 0%, #22c55e 100%);
}

.message-author {
    font-size: 0.9rem;
    font-weight: 600;
    color: #ffffff;
}

.message-time {
    font-size: 0.8rem;
    color: #64748b;
}

.message-content {
    background: #334155;
    padding: 15px;
    border-radius: 15px;
    color: #ffffff;
    line-height: 1.5;
}

.message-bubble.user .message-content {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
}

.message-bubble.ai .message-content {
    background: #334155;
}

.generated-image {
    max-width: 100%;
    border-radius: 10px;
    margin-top: 10px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
    cursor: pointer;
    transition: transform 0.3s ease;
}

.generated-image:hover {
    transform: scale(1.02);
}

.image-actions {
    display: flex;
    gap: 10px;
    margin-top: 10px;
}

.image-action-btn {
    background: rgba(74, 222, 128, 0.1);
    border: 1px solid rgba(74, 222, 128, 0.3);
    border-radius: 8px;
    color: #4ade80;
    padding: 8px 12px;
    font-size: 0.8rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.image-action-btn:hover {
    background: rgba(74, 222, 128, 0.2);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .sidebar {
        width: 240px;
    }

    .login-box {
        width: 90%;
        max-width: 400px;
        margin: 20px;
    }

    .input-container {
        flex-direction: column;
        align-items: stretch;
    }

    .input-actions {
        justify-content: flex-end;
        margin-top: 10px;
    }

    .message-bubble {
        max-width: 95%;
    }
}
