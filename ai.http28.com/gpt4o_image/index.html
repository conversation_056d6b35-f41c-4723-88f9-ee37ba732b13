<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GPT-4o Image VIP - AI图像生成平台</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <!-- 登录页面 -->
    <div id="loginPage" class="login-container">
        <div class="login-box">
            <div class="login-header">
                <div class="logo">
                    <i class="fas fa-robot"></i>
                    <h1>GPT-4o Image VIP</h1>
                </div>
                <p class="subtitle">AI图像生成平台</p>
            </div>
            
            <form id="loginForm" class="login-form">
                <div class="form-group">
                    <label for="username">用户名</label>
                    <input type="text" id="username" name="username" required placeholder="请输入用户名">
                </div>
                
                <div class="form-group">
                    <label for="password">密码</label>
                    <input type="password" id="password" name="password" required placeholder="请输入密码">
                </div>
                
                <button type="submit" class="login-btn">
                    <i class="fas fa-sign-in-alt"></i>
                    登录
                </button>
                
                <div id="loginError" class="error-message" style="display: none;"></div>
            </form>
        </div>
    </div>

    <!-- 主应用界面 -->
    <div id="mainApp" class="app-container" style="display: none;">
        <!-- 左侧边栏 -->
        <div class="sidebar">
            <div class="sidebar-header">
                <div class="server-icon">
                    <i class="fas fa-robot"></i>
                </div>
                <h3>GPT-4o Image</h3>
            </div>
            
            <div class="channel-list">
                <div class="channel-category">
                    <h4><i class="fas fa-hashtag"></i> 图像生成</h4>
                </div>
                <div class="channel-item active" data-channel="general">
                    <i class="fas fa-image"></i>
                    <span>通用生成</span>
                </div>
                <div class="channel-item" data-channel="creative">
                    <i class="fas fa-palette"></i>
                    <span>创意设计</span>
                </div>
                <div class="channel-item" data-channel="photo">
                    <i class="fas fa-camera"></i>
                    <span>照片风格</span>
                </div>
                <div class="channel-item" data-channel="art">
                    <i class="fas fa-paint-brush"></i>
                    <span>艺术创作</span>
                </div>
            </div>
            
            <div class="user-info">
                <div class="user-avatar">
                    <i class="fas fa-user"></i>
                </div>
                <div class="user-details">
                    <span class="username" id="currentUser"></span>
                    <span class="status">在线</span>
                </div>
                <button class="logout-btn" id="logoutBtn">
                    <i class="fas fa-sign-out-alt"></i>
                </button>
            </div>
        </div>

        <!-- 主内容区域 -->
        <div class="main-content">
            <!-- 顶部栏 -->
            <div class="content-header">
                <div class="channel-info">
                    <i class="fas fa-hashtag"></i>
                    <span id="currentChannelName">通用生成</span>
                </div>
                <div class="header-actions">
                    <button class="action-btn" title="设置">
                        <i class="fas fa-cog"></i>
                    </button>
                    <button class="action-btn" title="帮助">
                        <i class="fas fa-question-circle"></i>
                    </button>
                </div>
            </div>

            <!-- 消息/图像展示区域 -->
            <div class="messages-container" id="messagesContainer">
                <div class="welcome-message">
                    <div class="welcome-icon">
                        <i class="fas fa-magic"></i>
                    </div>
                    <h2>欢迎使用 GPT-4o Image VIP</h2>
                    <p>在下方输入您的图像描述，AI将为您生成精美的图像</p>
                </div>
            </div>

            <!-- 输入区域 -->
            <div class="input-area">
                <form id="imageForm" class="message-form">
                    <div class="input-container">
                        <textarea 
                            id="promptInput" 
                            placeholder="描述您想要生成的图像..." 
                            rows="1"
                            maxlength="1000"
                        ></textarea>
                        
                        <div class="input-actions">
                            <button type="button" class="action-btn" id="settingsBtn" title="生成设置">
                                <i class="fas fa-sliders-h"></i>
                            </button>
                            <button type="submit" class="send-btn" id="sendBtn">
                                <i class="fas fa-paper-plane"></i>
                            </button>
                        </div>
                    </div>
                    
                    <!-- 高级设置面板 -->
                    <div id="settingsPanel" class="settings-panel" style="display: none;">
                        <div class="settings-row">
                            <label for="imageSize">图像尺寸:</label>
                            <select id="imageSize">
                                <option value="1024x1024">1024x1024 (正方形)</option>
                                <option value="1792x1024">1792x1024 (横向)</option>
                                <option value="1024x1792">1024x1792 (纵向)</option>
                            </select>
                        </div>
                        
                        <div class="settings-row">
                            <label for="imageQuality">图像质量:</label>
                            <select id="imageQuality">
                                <option value="standard">标准</option>
                                <option value="hd">高清</option>
                            </select>
                        </div>
                        
                        <div class="settings-row">
                            <label for="imageStyle">图像风格:</label>
                            <select id="imageStyle">
                                <option value="vivid">生动</option>
                                <option value="natural">自然</option>
                            </select>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 加载动画 -->
    <div id="loadingOverlay" class="loading-overlay" style="display: none;">
        <div class="loading-spinner">
            <div class="spinner"></div>
            <p>AI正在生成图像，请稍候...</p>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
