<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GPT-4o Image VIP - AI图像生成平台</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <!-- 登录页面 -->
    <div id="loginPage" class="login-container">
        <div class="login-box">
            <div class="login-header">
                <div class="logo">
                    <i class="fas fa-robot"></i>
                    <h1>GPT-4o Image VIP</h1>
                </div>
                <p class="subtitle">AI图像生成平台</p>
            </div>

            <form id="loginForm" class="login-form">
                <div class="form-group">
                    <label for="username">用户名</label>
                    <input type="text" id="username" name="username" required placeholder="请输入用户名">
                </div>

                <div class="form-group">
                    <label for="password">密码</label>
                    <input type="password" id="password" name="password" required placeholder="请输入密码">
                </div>

                <button type="submit" class="login-btn">
                    <i class="fas fa-sign-in-alt"></i>
                    登录
                </button>

                <div id="loginError" class="error-message" style="display: none;"></div>
            </form>
        </div>
    </div>

    <!-- 调试状态栏 -->
    <div id="debugStatus" style="position: fixed; top: 0; left: 0; right: 0; background: #000; color: #0f0; padding: 5px; font-family: monospace; font-size: 12px; z-index: 10000; display: none;">
        <span id="debugText">调试模式</span>
        <button onclick="document.getElementById('debugStatus').style.display='none'" style="float: right; background: #f00; color: #fff; border: none; padding: 2px 8px;">关闭</button>
    </div>

    <!-- 主应用界面 -->
    <div id="mainApp" class="app-container" style="display: none;">
        <!-- 左侧边栏 -->
        <div class="sidebar">
            <div class="sidebar-header">
                <div class="server-icon">
                    <i class="fas fa-robot"></i>
                </div>
                <h3>GPT-4o Image</h3>
            </div>

            <div class="channel-list">
                <div class="channel-category">
                    <h4><i class="fas fa-hashtag"></i> 图像生成</h4>
                </div>
                <div class="channel-item active" data-channel="general">
                    <i class="fas fa-image"></i>
                    <span>通用生成</span>
                </div>
                <div class="channel-item" data-channel="creative">
                    <i class="fas fa-palette"></i>
                    <span>创意设计</span>
                </div>
                <div class="channel-item" data-channel="photo">
                    <i class="fas fa-camera"></i>
                    <span>照片风格</span>
                </div>
                <div class="channel-item" data-channel="art">
                    <i class="fas fa-paint-brush"></i>
                    <span>艺术创作</span>
                </div>
            </div>

            <div class="user-info">
                <div class="user-avatar">
                    <i class="fas fa-user"></i>
                </div>
                <div class="user-details">
                    <span class="username" id="currentUser"></span>
                    <span class="status">在线</span>
                </div>
                <button class="logout-btn" id="logoutBtn">
                    <i class="fas fa-sign-out-alt"></i>
                </button>
            </div>
        </div>

        <!-- 主内容区域 -->
        <div class="main-content">
            <!-- 顶部栏 -->
            <div class="content-header">
                <div class="channel-info">
                    <i class="fas fa-hashtag"></i>
                    <span id="currentChannelName">通用生成</span>
                </div>
                <div class="header-actions">
                    <button class="action-btn" title="设置">
                        <i class="fas fa-cog"></i>
                    </button>
                    <button class="action-btn" title="帮助">
                        <i class="fas fa-question-circle"></i>
                    </button>
                </div>
            </div>

            <!-- 消息/图像展示区域 -->
            <div class="messages-container" id="messagesContainer">
                <div class="welcome-message">
                    <div class="welcome-icon">
                        <i class="fas fa-magic"></i>
                    </div>
                    <h2>欢迎使用 GPT-4o Image VIP</h2>
                    <p>在下方输入您的图像描述，AI将为您生成精美的图像</p>
                </div>
            </div>

            <!-- 输入区域 -->
            <div class="input-area">
                <form id="imageForm" class="message-form">
                    <div class="input-container">
                        <textarea
                            id="promptInput"
                            placeholder="描述您想要生成的图像..."
                            rows="1"
                            maxlength="1000"
                        ></textarea>

                        <div class="input-actions">
                            <input type="file" id="imageUpload" accept="image/*" style="display: none;" onchange="console.log('文件选择事件触发', this.files); if(window.handleImageUpload) window.handleImageUpload(event);">
                            <button type="button" class="action-btn" id="uploadBtn" title="上传图片" onclick="console.log('上传按钮被点击'); document.getElementById('imageUpload').click();">
                                <i class="fas fa-image"></i>
                            </button>
                            <button type="button" class="action-btn" onclick="window.debugUpload()" title="调试上传" style="background: #ef4444;">
                                🐛
                            </button>
                            <button type="button" class="action-btn" id="settingsBtn" title="生成设置">
                                <i class="fas fa-sliders-h"></i>
                            </button>
                            <button type="submit" class="send-btn" id="sendBtn">
                                <i class="fas fa-paper-plane"></i>
                            </button>
                        </div>
                    </div>

                    <!-- 图片预览区域 -->
                    <div id="imagePreview" class="image-preview" style="display: none;">
                        <div class="preview-header">
                            <span>已上传图片:</span>
                            <button type="button" class="remove-image-btn" id="removeImageBtn">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                        <img id="previewImage" src="" alt="预览图片">
                    </div>

                    <!-- 高级设置面板 -->
                    <div id="settingsPanel" class="settings-panel" style="display: none;">
                        <div class="settings-row">
                            <label for="imageSize">图像尺寸:</label>
                            <select id="imageSize">
                                <option value="1024x1024">1024x1024 (正方形)</option>
                                <option value="1792x1024">1792x1024 (横向)</option>
                                <option value="1024x1792">1024x1792 (纵向)</option>
                            </select>
                        </div>

                        <div class="settings-row">
                            <label for="imageQuality">图像质量:</label>
                            <select id="imageQuality">
                                <option value="standard">标准</option>
                                <option value="hd">高清</option>
                            </select>
                        </div>

                        <div class="settings-row">
                            <label for="imageStyle">图像风格:</label>
                            <select id="imageStyle">
                                <option value="vivid">生动</option>
                                <option value="natural">自然</option>
                            </select>
                        </div>

                        <div class="settings-row">
                            <label for="generationType">生成类型:</label>
                            <select id="generationType">
                                <option value="create">创建新图像</option>
                                <option value="edit">编辑图像</option>
                                <option value="variation">图像变体</option>
                            </select>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 加载动画 -->
    <div id="loadingOverlay" class="loading-overlay" style="display: none;">
        <div class="loading-spinner">
            <div class="spinner"></div>
            <p>AI正在生成图像，请稍候...</p>
        </div>
    </div>

    <script src="script.js"></script>

    <!-- 调试信息 -->
    <script>
        // 添加调试信息到控制台
        window.addEventListener('load', function() {
            console.log('=== GPT-4o 调试信息 ===');
            console.log('页面加载完成时间:', new Date().toLocaleString());

            // 检查关键元素
            const elements = {
                uploadBtn: document.getElementById('uploadBtn'),
                imageUpload: document.getElementById('imageUpload'),
                imagePreview: document.getElementById('imagePreview'),
                previewImage: document.getElementById('previewImage'),
                removeImageBtn: document.getElementById('removeImageBtn')
            };

            console.log('DOM元素检查:', elements);

            // 检查全局函数
            console.log('全局函数检查:', {
                handleImageUpload: typeof window.handleImageUpload,
                removeUploadedImage: typeof window.removeUploadedImage
            });

            // 手动添加事件监听器作为备用
            if (elements.uploadBtn && elements.imageUpload) {
                elements.uploadBtn.addEventListener('click', function() {
                    console.log('备用事件：上传按钮被点击');
                    elements.imageUpload.click();
                });
                console.log('✅ 备用事件监听器已添加');
            } else {
                console.error('❌ 关键元素缺失');
            }

            if (elements.imageUpload) {
                elements.imageUpload.addEventListener('change', function(e) {
                    console.log('备用事件：文件选择变化', e.target.files);
                    if (window.handleImageUpload) {
                        window.handleImageUpload(e);
                    } else {
                        console.error('handleImageUpload 函数不存在');
                    }
                });
                console.log('✅ 备用文件选择事件已添加');
            }
        });

        // 添加全局调试函数
        window.debugUpload = function() {
            const uploadBtn = document.getElementById('uploadBtn');
            const imageUpload = document.getElementById('imageUpload');
            const debugStatus = document.getElementById('debugStatus');
            const debugText = document.getElementById('debugText');

            console.log('手动触发上传:', {
                uploadBtn: !!uploadBtn,
                imageUpload: !!imageUpload
            });

            // 显示调试状态
            if (debugStatus && debugText) {
                debugStatus.style.display = 'block';
                debugText.textContent = `调试: 上传按钮=${!!uploadBtn}, 文件输入=${!!imageUpload}, 时间=${new Date().toLocaleTimeString()}`;
            }

            if (imageUpload) {
                imageUpload.click();
                console.log('✅ 文件选择对话框已触发');
            } else {
                alert('❌ 上传元素未找到');
                if (debugText) {
                    debugText.textContent = '❌ 错误: 上传元素未找到';
                }
            }
        };

        // 添加全局状态更新函数
        window.updateDebugStatus = function(message) {
            const debugStatus = document.getElementById('debugStatus');
            const debugText = document.getElementById('debugText');

            if (debugStatus && debugText) {
                debugStatus.style.display = 'block';
                debugText.textContent = message;
                console.log('调试状态:', message);
            }
        };
    </script>
</body>
</html>
