<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图片上传测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f0f0f0;
        }
        .test-container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .upload-btn {
            background: #4ade80;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px;
        }
        .upload-btn:hover {
            background: #22c55e;
        }
        #preview {
            max-width: 100%;
            max-height: 300px;
            margin: 10px 0;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>图片上传功能测试</h1>
        
        <input type="file" id="fileInput" accept="image/*" class="hidden">
        <button class="upload-btn" id="uploadButton">📷 选择图片</button>
        
        <div id="result"></div>
        <img id="preview" class="hidden" alt="预览图片">
        
        <div id="info"></div>
    </div>

    <script>
        const fileInput = document.getElementById('fileInput');
        const uploadButton = document.getElementById('uploadButton');
        const preview = document.getElementById('preview');
        const result = document.getElementById('result');
        const info = document.getElementById('info');

        uploadButton.addEventListener('click', () => {
            console.log('按钮被点击');
            fileInput.click();
        });

        fileInput.addEventListener('change', (e) => {
            const file = e.target.files[0];
            if (!file) return;

            console.log('文件选择:', file);
            
            result.innerHTML = `
                <h3>文件信息:</h3>
                <p>文件名: ${file.name}</p>
                <p>文件大小: ${(file.size / 1024 / 1024).toFixed(2)} MB</p>
                <p>文件类型: ${file.type}</p>
            `;

            // 显示预览
            const reader = new FileReader();
            reader.onload = function(e) {
                preview.src = e.target.result;
                preview.classList.remove('hidden');
            };
            reader.readAsDataURL(file);
        });

        // 显示页面加载信息
        window.addEventListener('load', () => {
            info.innerHTML = `
                <h3>测试信息:</h3>
                <p>页面加载时间: ${new Date().toLocaleString()}</p>
                <p>上传按钮: ${uploadButton ? '✅ 找到' : '❌ 未找到'}</p>
                <p>文件输入: ${fileInput ? '✅ 找到' : '❌ 未找到'}</p>
            `;
        });
    </script>
</body>
</html>
