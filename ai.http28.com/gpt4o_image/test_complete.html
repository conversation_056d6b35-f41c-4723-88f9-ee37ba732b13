<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>完整功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f0f0f0;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .success { background: #d4edda; border-color: #c3e6cb; }
        .error { background: #f8d7da; border-color: #f5c6cb; }
        .info { background: #d1ecf1; border-color: #bee5eb; }
        button {
            background: #4ade80;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #22c55e;
        }
        .preview-area {
            margin: 10px 0;
            padding: 10px;
            border: 2px dashed #ddd;
            border-radius: 8px;
            text-align: center;
        }
        .preview-image {
            max-width: 200px;
            max-height: 200px;
            border-radius: 8px;
            margin: 10px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>GPT-4o Image VIP 完整功能测试</h1>
        
        <div class="test-section info">
            <h3>📋 测试说明</h3>
            <p>这个页面用于测试图片上传和预览功能的完整流程</p>
            <ul>
                <li>测试图片选择和上传</li>
                <li>测试图片预览显示</li>
                <li>测试消息中的图片显示</li>
                <li>测试图片移除功能</li>
            </ul>
        </div>
        
        <div class="test-section">
            <h3>🔧 步骤1: 测试图片上传</h3>
            <input type="file" id="testFileInput" accept="image/*" style="display: none;">
            <button onclick="testUpload()">📷 选择图片</button>
            <button onclick="clearTest()">🗑️ 清空测试</button>
            
            <div class="preview-area" id="previewArea">
                <p>选择图片后，预览将显示在这里</p>
            </div>
            
            <div id="uploadStatus"></div>
        </div>
        
        <div class="test-section">
            <h3>💬 步骤2: 测试消息显示</h3>
            <input type="text" id="testPrompt" placeholder="输入测试文字..." style="width: 300px; padding: 8px;">
            <button onclick="testMessageWithImage()">发送带图片的消息</button>
            
            <div id="messageArea" style="margin-top: 15px; padding: 15px; background: #f8f9fa; border-radius: 8px;">
                <p>消息预览将显示在这里</p>
            </div>
        </div>
        
        <div class="test-section">
            <h3>🎯 步骤3: 测试主应用</h3>
            <p>如果上述测试都通过，可以打开主应用进行完整测试：</p>
            <button onclick="window.open('index.html', '_blank')">🚀 打开主应用</button>
        </div>
        
        <div id="testResults"></div>
    </div>

    <script>
        let testImage = null;
        
        function testUpload() {
            const fileInput = document.getElementById('testFileInput');
            const previewArea = document.getElementById('previewArea');
            const uploadStatus = document.getElementById('uploadStatus');
            
            fileInput.addEventListener('change', function(e) {
                const file = e.target.files[0];
                if (!file) return;
                
                testImage = file;
                
                // 显示文件信息
                uploadStatus.innerHTML = `
                    <div class="success">
                        <h4>✅ 文件上传成功</h4>
                        <p>文件名: ${file.name}</p>
                        <p>文件大小: ${(file.size / 1024 / 1024).toFixed(2)} MB</p>
                        <p>文件类型: ${file.type}</p>
                    </div>
                `;
                
                // 显示预览
                const reader = new FileReader();
                reader.onload = function(e) {
                    previewArea.innerHTML = `
                        <img src="${e.target.result}" class="preview-image" alt="预览图片">
                        <p>✅ 图片预览成功</p>
                    `;
                };
                reader.readAsDataURL(file);
            });
            
            fileInput.click();
        }
        
        function testMessageWithImage() {
            const prompt = document.getElementById('testPrompt').value || '测试图片上传功能';
            const messageArea = document.getElementById('messageArea');
            
            if (!testImage) {
                messageArea.innerHTML = `
                    <div class="error">
                        <p>❌ 请先上传图片</p>
                    </div>
                `;
                return;
            }
            
            // 模拟消息显示
            const reader = new FileReader();
            reader.onload = function(e) {
                messageArea.innerHTML = `
                    <div style="border: 1px solid #4ade80; border-radius: 8px; padding: 15px; background: #f0fdf4;">
                        <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 10px;">
                            <div style="width: 32px; height: 32px; background: #4ade80; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white;">
                                👤
                            </div>
                            <span style="font-weight: bold;">测试用户</span>
                            <span style="color: #666; font-size: 0.9rem;">${new Date().toLocaleTimeString()}</span>
                        </div>
                        <div>
                            <p>${prompt}</p>
                            <div style="margin-top: 10px;">
                                <p style="font-size: 0.9rem; color: #666; margin-bottom: 8px;">📷 上传的图片:</p>
                                <img src="${e.target.result}" style="max-width: 200px; max-height: 200px; border-radius: 8px; border: 1px solid #4ade80;">
                            </div>
                        </div>
                    </div>
                `;
            };
            reader.readAsDataURL(testImage);
        }
        
        function clearTest() {
            testImage = null;
            document.getElementById('previewArea').innerHTML = '<p>选择图片后，预览将显示在这里</p>';
            document.getElementById('uploadStatus').innerHTML = '';
            document.getElementById('messageArea').innerHTML = '<p>消息预览将显示在这里</p>';
            document.getElementById('testFileInput').value = '';
        }
        
        // 页面加载时显示测试信息
        window.addEventListener('load', () => {
            const results = document.getElementById('testResults');
            results.innerHTML = `
                <div class="test-section info">
                    <h3>📊 测试环境信息</h3>
                    <p>浏览器: ${navigator.userAgent}</p>
                    <p>当前时间: ${new Date().toLocaleString()}</p>
                    <p>页面URL: ${window.location.href}</p>
                    <p>文件API支持: ${window.FileReader ? '✅ 支持' : '❌ 不支持'}</p>
                </div>
            `;
        });
    </script>
</body>
</html>
