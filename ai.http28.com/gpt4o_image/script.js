// 配置常量
const CONFIG = {
    BACKEND_URL: '',  // 使用相对路径，通过Nginx代理
    OPENAI_API_URL: 'https://xuedingmao.online/v1',
    OPENAI_API_KEY: 'sk-o6kaAOCW2lV5z5gf3CY7hlRYc5gSmcrOaS9L02tNfJdKCYhN'
};

// 全局状态
let currentUser = null;
let authToken = null;
let currentChannel = 'general';

// DOM元素
const loginPage = document.getElementById('loginPage');
const mainApp = document.getElementById('mainApp');
const loginForm = document.getElementById('loginForm');
const loginError = document.getElementById('loginError');
const messagesContainer = document.getElementById('messagesContainer');
const imageForm = document.getElementById('imageForm');
const promptInput = document.getElementById('promptInput');
const sendBtn = document.getElementById('sendBtn');
const settingsBtn = document.getElementById('settingsBtn');
const settingsPanel = document.getElementById('settingsPanel');
const loadingOverlay = document.getElementById('loadingOverlay');
const logoutBtn = document.getElementById('logoutBtn');
const currentUserSpan = document.getElementById('currentUser');
const currentChannelName = document.getElementById('currentChannelName');

// 初始化应用
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
    setupEventListeners();
    checkAuthStatus();
});

// 初始化应用
function initializeApp() {
    const savedToken = localStorage.getItem('authToken');
    const savedUser = localStorage.getItem('currentUser');

    if (savedToken && savedUser) {
        authToken = savedToken;
        currentUser = JSON.parse(savedUser);
        showMainApp();
    } else {
        showLoginPage();
    }
}

// 设置事件监听器
function setupEventListeners() {
    loginForm.addEventListener('submit', handleLogin);
    imageForm.addEventListener('submit', handleImageGeneration);
    settingsBtn.addEventListener('click', toggleSettingsPanel);
    logoutBtn.addEventListener('click', handleLogout);

    document.querySelectorAll('.channel-item').forEach(item => {
        item.addEventListener('click', function() {
            switchChannel(this.dataset.channel, this.textContent.trim());
        });
    });

    promptInput.addEventListener('input', autoResizeTextarea);
    promptInput.addEventListener('keydown', function(e) {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            handleImageGeneration(e);
        }
    });
}

// 检查认证状态
async function checkAuthStatus() {
    if (!authToken) return;

    try {
        const response = await fetch(`${CONFIG.BACKEND_URL}/api/auth/verify`, {
            headers: { 'Authorization': `Bearer ${authToken}` }
        });

        if (response.ok) {
            const data = await response.json();
            if (data.success) {
                currentUser = data.user;
                showMainApp();
                return;
            }
        }
        handleLogout();
    } catch (error) {
        console.error('认证检查失败:', error);
        handleLogout();
    }
}

// 处理登录
async function handleLogin(e) {
    e.preventDefault();

    const username = document.getElementById('username').value;
    const password = document.getElementById('password').value;

    if (!username || !password) {
        showError('请输入用户名和密码');
        return;
    }

    try {
        showLoading(true);

        const response = await fetch(`${CONFIG.BACKEND_URL}/api/auth/login`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ username, password })
        });

        const data = await response.json();

        if (data.success) {
            authToken = data.access_token;
            currentUser = data.user;

            localStorage.setItem('authToken', authToken);
            localStorage.setItem('currentUser', JSON.stringify(currentUser));

            showMainApp();
            hideError();
        } else {
            showError(data.message || '登录失败');
        }
    } catch (error) {
        console.error('登录错误:', error);
        showError('网络连接失败，请检查网络设置');
    } finally {
        showLoading(false);
    }
}

// 处理登出
function handleLogout() {
    authToken = null;
    currentUser = null;
    localStorage.removeItem('authToken');
    localStorage.removeItem('currentUser');
    showLoginPage();
    clearMessages();
}

// 显示/隐藏页面
function showLoginPage() {
    loginPage.style.display = 'flex';
    mainApp.style.display = 'none';
}

function showMainApp() {
    loginPage.style.display = 'none';
    mainApp.style.display = 'flex';

    if (currentUser) {
        currentUserSpan.textContent = currentUser.username;
    }

    if (messagesContainer.children.length <= 1) {
        addWelcomeMessage();
    }
}

// 错误处理
function showError(message) {
    loginError.textContent = message;
    loginError.style.display = 'block';
}

function hideError() {
    loginError.style.display = 'none';
}

// 加载动画
function showLoading(show) {
    loadingOverlay.style.display = show ? 'flex' : 'none';
}

// 设置面板
function toggleSettingsPanel() {
    const isVisible = settingsPanel.style.display !== 'none';
    settingsPanel.style.display = isVisible ? 'none' : 'block';
}

// 频道切换
function switchChannel(channelId, channelName) {
    currentChannel = channelId;
    currentChannelName.textContent = channelName;

    document.querySelectorAll('.channel-item').forEach(item => {
        item.classList.remove('active');
    });
    document.querySelector(`[data-channel="${channelId}"]`).classList.add('active');

    clearMessages();
    addChannelWelcomeMessage(channelName);
}

// 处理图像生成
async function handleImageGeneration(e) {
    e.preventDefault();

    const prompt = promptInput.value.trim();
    if (!prompt) return;

    const size = document.getElementById('imageSize').value;
    const quality = document.getElementById('imageQuality').value;
    const style = document.getElementById('imageStyle').value;

    addMessage('user', currentUser.username, prompt);
    promptInput.value = '';
    autoResizeTextarea();
    sendBtn.disabled = true;

    try {
        const thinkingMessageId = addMessage('ai', 'GPT-4o', '正在生成图像，请稍候...');

        const response = await fetch(`${CONFIG.OPENAI_API_URL}/images/generations`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${CONFIG.OPENAI_API_KEY}`
            },
            body: JSON.stringify({
                model: 'dall-e-3',
                prompt: prompt,
                size: size,
                quality: quality,
                style: style,
                n: 1
            })
        });

        const data = await response.json();

        if (response.ok && data.data && data.data.length > 0) {
            removeMessage(thinkingMessageId);
            addImageMessage('ai', 'GPT-4o', data.data[0].url, prompt);
        } else {
            updateMessage(thinkingMessageId, '抱歉，图像生成失败。请稍后重试。');
        }
    } catch (error) {
        console.error('图像生成错误:', error);
        addMessage('ai', 'GPT-4o', '抱歉，图像生成失败。请检查网络连接或稍后重试。');
    } finally {
        sendBtn.disabled = false;
    }
}

// 自动调整文本框高度
function autoResizeTextarea() {
    promptInput.style.height = 'auto';
    promptInput.style.height = Math.min(promptInput.scrollHeight, 120) + 'px';
}

// 添加消息
function addMessage(type, author, content) {
    const messageId = 'msg_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    const messageDiv = document.createElement('div');
    messageDiv.className = `message-bubble ${type}`;
    messageDiv.id = messageId;

    const time = new Date().toLocaleTimeString('zh-CN', {
        hour: '2-digit',
        minute: '2-digit'
    });

    messageDiv.innerHTML = `
        <div class="message-header">
            <div class="message-avatar">
                <i class="fas fa-${type === 'user' ? 'user' : 'robot'}"></i>
            </div>
            <span class="message-author">${author}</span>
            <span class="message-time">${time}</span>
        </div>
        <div class="message-content">${content}</div>
    `;

    const welcomeMessage = messagesContainer.querySelector('.welcome-message');
    if (welcomeMessage) {
        welcomeMessage.remove();
    }

    messagesContainer.appendChild(messageDiv);
    messagesContainer.scrollTop = messagesContainer.scrollHeight;

    return messageId;
}

// 添加图像消息
function addImageMessage(type, author, imageUrl, prompt) {
    const messageId = addMessage(type, author, '');
    const messageDiv = document.getElementById(messageId);
    const messageContent = messageDiv.querySelector('.message-content');

    messageContent.innerHTML = `
        <p>已为您生成图像：</p>
        <img src="${imageUrl}" alt="${prompt}" class="generated-image" onclick="openImageModal('${imageUrl}')">
        <div class="image-actions">
            <button class="image-action-btn" onclick="downloadImage('${imageUrl}', '${prompt}')">
                <i class="fas fa-download"></i> 下载
            </button>
            <button class="image-action-btn" onclick="copyImageUrl('${imageUrl}')">
                <i class="fas fa-copy"></i> 复制链接
            </button>
        </div>
    `;

    return messageId;
}

// 更新消息
function updateMessage(messageId, newContent) {
    const messageDiv = document.getElementById(messageId);
    if (messageDiv) {
        const messageContent = messageDiv.querySelector('.message-content');
        messageContent.textContent = newContent;
    }
}

// 移除消息
function removeMessage(messageId) {
    const messageDiv = document.getElementById(messageId);
    if (messageDiv) {
        messageDiv.remove();
    }
}

// 清空消息
function clearMessages() {
    messagesContainer.innerHTML = '';
}

// 添加欢迎消息
function addWelcomeMessage() {
    messagesContainer.innerHTML = `
        <div class="welcome-message">
            <div class="welcome-icon">
                <i class="fas fa-magic"></i>
            </div>
            <h2>欢迎使用 GPT-4o Image VIP</h2>
            <p>在下方输入您的图像描述，AI将为您生成精美的图像</p>
        </div>
    `;
}

// 添加频道特定欢迎消息
function addChannelWelcomeMessage(channelName) {
    const channelDescriptions = {
        '通用生成': '适合各种类型的图像生成需求',
        '创意设计': '专注于创意和设计类图像',
        '照片风格': '生成逼真的照片风格图像',
        '艺术创作': '创作艺术风格的图像作品'
    };

    messagesContainer.innerHTML = `
        <div class="welcome-message">
            <div class="welcome-icon">
                <i class="fas fa-hashtag"></i>
            </div>
            <h2>${channelName}</h2>
            <p>${channelDescriptions[channelName] || '开始您的AI图像生成之旅'}</p>
        </div>
    `;
}

// 下载图像
function downloadImage(imageUrl, prompt) {
    const link = document.createElement('a');
    link.href = imageUrl;
    link.download = `gpt4o_image_${prompt.substring(0, 20)}_${Date.now()}.png`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

// 复制图像链接
function copyImageUrl(imageUrl) {
    navigator.clipboard.writeText(imageUrl).then(() => {
        console.log('图像链接已复制到剪贴板');
    });
}

// 打开图像模态框
function openImageModal(imageUrl) {
    window.open(imageUrl, '_blank');
}
