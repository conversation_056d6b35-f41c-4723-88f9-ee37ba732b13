// 配置常量
const CONFIG = {
    BACKEND_URL: '',  // 使用相对路径，通过Nginx代理
    OPENAI_API_URL: 'https://xuedingmao.online/v1',
    OPENAI_API_KEY: 'sk-o6kaAOCW2lV5z5gf3CY7hlRYc5gSmcrOaS9L02tNfJdKCYhN'
};

// 全局状态
let currentUser = null;
let authToken = null;
let currentChannel = 'general';
let uploadedImage = null;

// 确保全局变量可访问
window.uploadedImage = uploadedImage;

// DOM元素 - 将在DOMContentLoaded后获取
let loginPage, mainApp, loginForm, loginError, messagesContainer, imageForm;
let promptInput, sendBtn, settingsBtn, settingsPanel, loadingOverlay, logoutBtn;
let currentUserSpan, currentChannelName, uploadBtn, imageUpload, imagePreview;
let previewImage, removeImageBtn;

// 初始化应用
document.addEventListener('DOMContentLoaded', function() {
    // 获取DOM元素
    getDOMElements();
    initializeApp();
    setupEventListeners();
    checkAuthStatus();
});

// 获取DOM元素
function getDOMElements() {
    loginPage = document.getElementById('loginPage');
    mainApp = document.getElementById('mainApp');
    loginForm = document.getElementById('loginForm');
    loginError = document.getElementById('loginError');
    messagesContainer = document.getElementById('messagesContainer');
    imageForm = document.getElementById('imageForm');
    promptInput = document.getElementById('promptInput');
    sendBtn = document.getElementById('sendBtn');
    settingsBtn = document.getElementById('settingsBtn');
    settingsPanel = document.getElementById('settingsPanel');
    loadingOverlay = document.getElementById('loadingOverlay');
    logoutBtn = document.getElementById('logoutBtn');
    currentUserSpan = document.getElementById('currentUser');
    currentChannelName = document.getElementById('currentChannelName');
    uploadBtn = document.getElementById('uploadBtn');
    imageUpload = document.getElementById('imageUpload');
    imagePreview = document.getElementById('imagePreview');
    previewImage = document.getElementById('previewImage');
    removeImageBtn = document.getElementById('removeImageBtn');

    console.log('DOM元素获取完成:', {
        uploadBtn: !!uploadBtn,
        imageUpload: !!imageUpload,
        imagePreview: !!imagePreview
    });
}

// 初始化应用
function initializeApp() {
    const savedToken = localStorage.getItem('authToken');
    const savedUser = localStorage.getItem('currentUser');

    if (savedToken && savedUser) {
        authToken = savedToken;
        currentUser = JSON.parse(savedUser);
        showMainApp();
    } else {
        showLoginPage();
    }
}

// 设置事件监听器
function setupEventListeners() {
    if (loginForm) loginForm.addEventListener('submit', handleLogin);
    if (imageForm) imageForm.addEventListener('submit', handleImageGeneration);
    if (settingsBtn) settingsBtn.addEventListener('click', toggleSettingsPanel);
    if (logoutBtn) logoutBtn.addEventListener('click', handleLogout);

    // 简单的图片上传处理
    if (imageUpload) {
        imageUpload.addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                // 检查文件格式 - 根据薛定猫API要求必须是PNG
                if (!file.type.includes('png')) {
                    alert('根据API要求，图像编辑和变体功能只支持PNG格式的图片！');
                    return;
                }

                // 检查文件大小
                if (file.size > 4 * 1024 * 1024) {
                    alert('图片文件大小不能超过4MB！');
                    return;
                }

                uploadedImage = file;
                showImagePreview(file);
                // 自动切换到编辑模式
                const generationType = document.getElementById('generationType');
                if (generationType) generationType.value = 'edit';

                console.log('图片上传成功:', {
                    name: file.name,
                    type: file.type,
                    size: file.size
                });
            }
        });
    }

    document.querySelectorAll('.channel-item').forEach(item => {
        item.addEventListener('click', function() {
            switchChannel(this.dataset.channel, this.textContent.trim());
        });
    });

    if (promptInput) {
        promptInput.addEventListener('input', autoResizeTextarea);
        promptInput.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                handleImageGeneration(e);
            }
        });
    }
}

// 显示图片预览
function showImagePreview(file) {
    const reader = new FileReader();
    reader.onload = function(e) {
        const previewDiv = document.getElementById('imagePreview');
        const previewImg = document.getElementById('previewImage');

        if (previewDiv && previewImg) {
            previewImg.src = e.target.result;
            previewDiv.style.display = 'block';
        }
    };
    reader.readAsDataURL(file);
}

// 检查认证状态
async function checkAuthStatus() {
    if (!authToken) return;

    try {
        const response = await fetch(`${CONFIG.BACKEND_URL}/api/auth/verify`, {
            headers: { 'Authorization': `Bearer ${authToken}` }
        });

        if (response.ok) {
            const data = await response.json();
            if (data.success) {
                currentUser = data.user;
                showMainApp();
                return;
            }
        }
        handleLogout();
    } catch (error) {
        console.error('认证检查失败:', error);
        handleLogout();
    }
}

// 处理登录
async function handleLogin(e) {
    e.preventDefault();

    const username = document.getElementById('username').value;
    const password = document.getElementById('password').value;

    if (!username || !password) {
        showError('请输入用户名和密码');
        return;
    }

    try {
        showLoading(true);

        const response = await fetch(`${CONFIG.BACKEND_URL}/api/auth/login`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ username, password })
        });

        const data = await response.json();

        if (data.success) {
            authToken = data.access_token;
            currentUser = data.user;

            localStorage.setItem('authToken', authToken);
            localStorage.setItem('currentUser', JSON.stringify(currentUser));

            showMainApp();
            hideError();
        } else {
            showError(data.message || '登录失败');
        }
    } catch (error) {
        console.error('登录错误:', error);
        showError('网络连接失败，请检查网络设置');
    } finally {
        showLoading(false);
    }
}

// 处理登出
function handleLogout() {
    authToken = null;
    currentUser = null;
    localStorage.removeItem('authToken');
    localStorage.removeItem('currentUser');
    showLoginPage();
    clearMessages();
}

// 显示/隐藏页面
function showLoginPage() {
    loginPage.style.display = 'flex';
    mainApp.style.display = 'none';
}

function showMainApp() {
    loginPage.style.display = 'none';
    mainApp.style.display = 'flex';

    if (currentUser) {
        currentUserSpan.textContent = currentUser.username;
    }

    if (messagesContainer.children.length <= 1) {
        addWelcomeMessage();
    }
}

// 错误处理
function showError(message) {
    loginError.textContent = message;
    loginError.style.display = 'block';
}

function hideError() {
    loginError.style.display = 'none';
}

// 加载动画
function showLoading(show) {
    loadingOverlay.style.display = show ? 'flex' : 'none';
}

// 设置面板
function toggleSettingsPanel() {
    const isVisible = settingsPanel.style.display !== 'none';
    settingsPanel.style.display = isVisible ? 'none' : 'block';
}

// 频道切换
function switchChannel(channelId, channelName) {
    currentChannel = channelId;
    currentChannelName.textContent = channelName;

    document.querySelectorAll('.channel-item').forEach(item => {
        item.classList.remove('active');
    });
    document.querySelector(`[data-channel="${channelId}"]`).classList.add('active');

    clearMessages();
    addChannelWelcomeMessage(channelName);
}

// 处理图像生成
async function handleImageGeneration(e) {
    e.preventDefault();

    const prompt = promptInput.value.trim();
    const generationType = document.getElementById('generationType').value;

    // 根据生成类型检查必要条件
    if (generationType === 'create' && !prompt) {
        return;
    }

    if ((generationType === 'edit' || generationType === 'variation') && !uploadedImage) {
        alert('请先上传一张图片');
        return;
    }

    // 调试：检查图片文件
    if (uploadedImage) {
        console.log('发送时的图片文件:', {
            name: uploadedImage.name,
            size: uploadedImage.size,
            type: uploadedImage.type,
            lastModified: uploadedImage.lastModified
        });
    }

    const size = document.getElementById('imageSize').value;
    const quality = document.getElementById('imageQuality').value;
    const style = document.getElementById('imageStyle').value;

    // 添加用户消息（包含图片预览）
    if (uploadedImage) {
        addUserMessageWithImage(prompt, uploadedImage);
    } else {
        addMessage('user', currentUser.username, prompt);
    }

    promptInput.value = '';
    autoResizeTextarea();
    sendBtn.disabled = true;

    try {
        const thinkingMessageId = addMessage('ai', 'GPT-4o', '正在生成图像，请稍候...');

        let apiEndpoint;
        let requestBody;

        if (generationType === 'create') {
            // 创建新图像
            apiEndpoint = `${CONFIG.OPENAI_API_URL}/images/generations`;
            requestBody = JSON.stringify({
                model: 'dall-e-3',
                prompt: prompt,
                size: size,
                quality: quality,
                style: style,
                n: 1
            });
        } else if (generationType === 'edit') {
            // 编辑图像 - 严格按照薛定猫API文档格式
            apiEndpoint = `${CONFIG.OPENAI_API_URL}/images/edits`;
            const formData = new FormData();
            formData.append('image', uploadedImage, uploadedImage.name);
            formData.append('prompt', prompt);
            formData.append('n', '1');  // 注意：文档要求是字符串类型
            formData.append('size', size);
            formData.append('response_format', 'url');
            requestBody = formData;
            console.log('图像编辑请求参数:', {
                fileName: uploadedImage.name,
                fileType: uploadedImage.type,
                fileSize: uploadedImage.size,
                prompt: prompt,
                size: size,
                n: '1',
                response_format: 'url'
            });
        } else if (generationType === 'variation') {
            // 图像变体 - 严格按照薛定猫API文档格式
            apiEndpoint = `${CONFIG.OPENAI_API_URL}/images/variations`;
            const formData = new FormData();
            formData.append('image', uploadedImage, uploadedImage.name);
            formData.append('n', '1');  // 注意：文档要求是字符串类型
            formData.append('size', size);
            formData.append('response_format', 'url');
            requestBody = formData;
            console.log('图像变体请求参数:', {
                fileName: uploadedImage.name,
                fileType: uploadedImage.type,
                fileSize: uploadedImage.size,
                size: size,
                n: '1',
                response_format: 'url'
            });
        }

        const headers = {
            'Authorization': `Bearer ${CONFIG.OPENAI_API_KEY}`
        };

        // 只有JSON请求才需要Content-Type
        if (generationType === 'create') {
            headers['Content-Type'] = 'application/json';
        }

        const response = await fetch(apiEndpoint, {
            method: 'POST',
            headers: headers,
            body: requestBody
        });

        const data = await response.json();

        if (response.ok && data.data && data.data.length > 0) {
            removeMessage(thinkingMessageId);
            addImageMessage('ai', 'GPT-4o', data.data[0].url, prompt || '图像变体');
        } else {
            updateMessage(thinkingMessageId, '抱歉，图像生成失败。请稍后重试。');
            console.error('API错误:', data);
        }
    } catch (error) {
        console.error('图像生成错误:', error);
        addMessage('ai', 'GPT-4o', '抱歉，图像生成失败。请检查网络连接或稍后重试。');
    } finally {
        sendBtn.disabled = false;
    }
}

// 处理图片上传
function handleImageUpload(e) {
    console.log('handleImageUpload 被调用', e);

    // 更新调试状态
    if (window.updateDebugStatus) {
        window.updateDebugStatus('📷 图片上传函数被调用');
    }

    const file = e.target.files[0];
    if (!file) {
        console.log('没有选择文件');
        if (window.updateDebugStatus) {
            window.updateDebugStatus('❌ 没有选择文件');
        }
        return;
    }

    console.log('选择的文件:', file);

    // 检查文件类型
    if (!file.type.startsWith('image/')) {
        alert('请选择图片文件');
        return;
    }

    // 检查文件大小 (最大4MB)
    if (file.size > 4 * 1024 * 1024) {
        alert('图片文件大小不能超过4MB');
        return;
    }

    uploadedImage = file;
    console.log('图片已保存到 uploadedImage');

    // 更新调试状态
    if (window.updateDebugStatus) {
        window.updateDebugStatus(`✅ 文件已选择: ${file.name} (${(file.size/1024/1024).toFixed(2)}MB)`);
    }

    // 动态获取预览元素（防止DOM元素获取失败）
    const currentPreviewImage = document.getElementById('previewImage');
    const currentImagePreview = document.getElementById('imagePreview');

    console.log('预览元素检查:', {
        previewImage: !!currentPreviewImage,
        imagePreview: !!currentImagePreview
    });

    // 显示预览
    const reader = new FileReader();
    reader.onload = function(e) {
        console.log('图片读取完成，显示预览');
        if (currentPreviewImage && currentImagePreview) {
            currentPreviewImage.src = e.target.result;
            currentImagePreview.style.display = 'block';
            console.log('预览图片已显示');

            // 更新调试状态
            if (window.updateDebugStatus) {
                window.updateDebugStatus('🖼️ 图片预览已显示');
            }

            // 自动切换到编辑模式
            const generationType = document.getElementById('generationType');
            if (generationType) {
                generationType.value = 'edit';
                console.log('已自动切换到编辑模式');
            }

            // 显示成功提示
            showUploadSuccess(file.name);
        } else {
            console.error('预览元素未找到:', {
                currentPreviewImage: !!currentPreviewImage,
                currentImagePreview: !!currentImagePreview
            });

            // 更新调试状态
            if (window.updateDebugStatus) {
                window.updateDebugStatus('❌ 预览元素未找到，但图片已上传');
            }

            alert('图片预览功能异常，但图片已上传成功');
        }
    };
    reader.onerror = function() {
        console.error('图片读取失败');
        alert('图片读取失败，请重试');
    };
    reader.readAsDataURL(file);
}

// 显示上传成功提示
function showUploadSuccess(fileName) {
    // 创建临时提示
    const toast = document.createElement('div');
    toast.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: #4ade80;
        color: white;
        padding: 12px 20px;
        border-radius: 8px;
        z-index: 9999;
        font-size: 14px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.3);
    `;
    toast.textContent = `✅ 图片上传成功: ${fileName}`;
    document.body.appendChild(toast);

    // 3秒后自动移除
    setTimeout(() => {
        if (toast.parentNode) {
            toast.parentNode.removeChild(toast);
        }
    }, 3000);
}

// 移除上传的图片
function removeUploadedImage() {
    console.log('移除上传的图片');
    uploadedImage = null;

    // 动态获取元素
    const currentImagePreview = document.getElementById('imagePreview');
    const currentPreviewImage = document.getElementById('previewImage');
    const currentImageUpload = document.getElementById('imageUpload');
    const currentGenerationType = document.getElementById('generationType');

    if (currentImagePreview) {
        currentImagePreview.style.display = 'none';
    }
    if (currentPreviewImage) {
        currentPreviewImage.src = '';
    }
    if (currentImageUpload) {
        currentImageUpload.value = '';
    }

    // 重置生成类型为创建新图像
    if (currentGenerationType) {
        currentGenerationType.value = 'create';
    }

    console.log('图片已移除');
}

// 自动调整文本框高度
function autoResizeTextarea() {
    promptInput.style.height = 'auto';
    promptInput.style.height = Math.min(promptInput.scrollHeight, 120) + 'px';
}

// 添加消息
function addMessage(type, author, content) {
    const messageId = 'msg_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    const messageDiv = document.createElement('div');
    messageDiv.className = `message-bubble ${type}`;
    messageDiv.id = messageId;

    const time = new Date().toLocaleTimeString('zh-CN', {
        hour: '2-digit',
        minute: '2-digit'
    });

    messageDiv.innerHTML = `
        <div class="message-header">
            <div class="message-avatar">
                <i class="fas fa-${type === 'user' ? 'user' : 'robot'}"></i>
            </div>
            <span class="message-author">${author}</span>
            <span class="message-time">${time}</span>
        </div>
        <div class="message-content">${content}</div>
    `;

    const welcomeMessage = messagesContainer.querySelector('.welcome-message');
    if (welcomeMessage) {
        welcomeMessage.remove();
    }

    messagesContainer.appendChild(messageDiv);
    messagesContainer.scrollTop = messagesContainer.scrollHeight;

    return messageId;
}

// 添加带图片的用户消息
function addUserMessageWithImage(prompt, imageFile) {
    const messageId = addMessage('user', currentUser.username, '');
    const messageDiv = document.getElementById(messageId);
    const messageContent = messageDiv.querySelector('.message-content');

    // 创建图片预览URL
    const reader = new FileReader();
    reader.onload = function(e) {
        const imageDataUrl = e.target.result;

        let content = '';
        if (prompt) {
            content += `<p>${prompt}</p>`;
        }

        content += `
            <div style="margin-top: 10px;">
                <p style="font-size: 0.9rem; color: #94a3b8; margin-bottom: 8px;">📷 上传的图片:</p>
                <img src="${imageDataUrl}" alt="用户上传的图片" style="max-width: 150px; max-height: 120px; width: auto; height: auto; border-radius: 8px; border: 1px solid #065f46; object-fit: contain;">
            </div>
        `;

        messageContent.innerHTML = content;
    };
    reader.readAsDataURL(imageFile);

    return messageId;
}

// 添加图像消息
function addImageMessage(type, author, imageUrl, prompt) {
    const messageId = addMessage(type, author, '');
    const messageDiv = document.getElementById(messageId);
    const messageContent = messageDiv.querySelector('.message-content');

    messageContent.innerHTML = `
        <p>已为您生成图像：</p>
        <img src="${imageUrl}" alt="${prompt}" class="generated-image" onclick="openImageModal('${imageUrl}')">
        <div class="image-actions">
            <button class="image-action-btn" onclick="downloadImage('${imageUrl}', '${prompt}')">
                <i class="fas fa-download"></i> 下载
            </button>
            <button class="image-action-btn" onclick="copyImageUrl('${imageUrl}')">
                <i class="fas fa-copy"></i> 复制链接
            </button>
        </div>
    `;

    return messageId;
}

// 更新消息
function updateMessage(messageId, newContent) {
    const messageDiv = document.getElementById(messageId);
    if (messageDiv) {
        const messageContent = messageDiv.querySelector('.message-content');
        messageContent.textContent = newContent;
    }
}

// 移除消息
function removeMessage(messageId) {
    const messageDiv = document.getElementById(messageId);
    if (messageDiv) {
        messageDiv.remove();
    }
}

// 清空消息
function clearMessages() {
    messagesContainer.innerHTML = '';
}

// 添加欢迎消息
function addWelcomeMessage() {
    messagesContainer.innerHTML = `
        <div class="welcome-message">
            <div class="welcome-icon">
                <i class="fas fa-magic"></i>
            </div>
            <h2>欢迎使用 GPT-4o Image VIP</h2>
            <p>在下方输入您的图像描述，AI将为您生成精美的图像</p>
        </div>
    `;
}

// 添加频道特定欢迎消息
function addChannelWelcomeMessage(channelName) {
    const channelDescriptions = {
        '通用生成': '适合各种类型的图像生成需求',
        '创意设计': '专注于创意和设计类图像',
        '照片风格': '生成逼真的照片风格图像',
        '艺术创作': '创作艺术风格的图像作品'
    };

    messagesContainer.innerHTML = `
        <div class="welcome-message">
            <div class="welcome-icon">
                <i class="fas fa-hashtag"></i>
            </div>
            <h2>${channelName}</h2>
            <p>${channelDescriptions[channelName] || '开始您的AI图像生成之旅'}</p>
        </div>
    `;
}

// 下载图像
function downloadImage(imageUrl, prompt) {
    const link = document.createElement('a');
    link.href = imageUrl;
    link.download = `gpt4o_image_${prompt.substring(0, 20)}_${Date.now()}.png`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

// 复制图像链接
function copyImageUrl(imageUrl) {
    navigator.clipboard.writeText(imageUrl).then(() => {
        console.log('图像链接已复制到剪贴板');
    });
}

// 打开图像模态框
function openImageModal(imageUrl) {
    window.open(imageUrl, '_blank');
}

// 暴露函数到全局作用域以便HTML内联事件调用
window.handleImageUpload = handleImageUpload;
window.removeUploadedImage = removeUploadedImage;
window.addUserMessageWithImage = addUserMessageWithImage;
window.showUploadSuccess = showUploadSuccess;
window.downloadImage = downloadImage;
window.copyImageUrl = copyImageUrl;
window.openImageModal = openImageModal;
