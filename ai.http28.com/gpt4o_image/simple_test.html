<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单上传测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f0f0f0;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .upload-area {
            border: 2px dashed #4ade80;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            margin: 20px 0;
            background: #f0fdf4;
        }
        .upload-btn {
            background: #4ade80;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
        }
        .upload-btn:hover {
            background: #22c55e;
        }
        .preview {
            margin: 20px 0;
            text-align: center;
        }
        .preview img {
            max-width: 300px;
            max-height: 300px;
            border-radius: 8px;
            border: 1px solid #ddd;
        }
        .status {
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
            font-weight: bold;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 图片上传功能测试</h1>
        
        <div class="upload-area">
            <h3>📷 选择图片文件</h3>
            <p>支持 JPG, PNG, GIF 格式，最大 4MB</p>
            
            <!-- 隐藏的文件输入 -->
            <input type="file" id="fileInput" accept="image/*" style="display: none;" onchange="handleFileUpload(this)">
            
            <!-- 多种上传方式 -->
            <button class="upload-btn" onclick="document.getElementById('fileInput').click()">
                📁 点击选择文件
            </button>
            
            <br>
            
            <!-- 直接的文件输入 -->
            <label for="fileInput2" class="upload-btn" style="display: inline-block;">
                📂 直接文件选择
            </label>
            <input type="file" id="fileInput2" accept="image/*" onchange="handleFileUpload(this)" style="margin: 10px;">
        </div>
        
        <div id="status"></div>
        
        <div id="preview" class="preview" style="display: none;">
            <h3>🖼️ 图片预览</h3>
            <img id="previewImg" src="" alt="预览图片">
            <br>
            <button class="upload-btn" onclick="clearPreview()" style="background: #ef4444;">
                🗑️ 清除图片
            </button>
        </div>
        
        <div class="info status">
            <h4>📋 测试说明</h4>
            <ul>
                <li>尝试点击"点击选择文件"按钮</li>
                <li>或者使用"直接文件选择"输入框</li>
                <li>选择图片后应该显示预览</li>
                <li>查看状态信息确认上传成功</li>
            </ul>
        </div>
        
        <div style="margin-top: 30px; text-align: center;">
            <button class="upload-btn" onclick="window.open('index.html', '_blank')">
                🚀 打开主应用测试
            </button>
        </div>
    </div>

    <script>
        let uploadedFile = null;
        
        function handleFileUpload(input) {
            console.log('文件上传处理函数被调用', input);
            
            const file = input.files[0];
            const statusDiv = document.getElementById('status');
            const previewDiv = document.getElementById('preview');
            const previewImg = document.getElementById('previewImg');
            
            if (!file) {
                showStatus('❌ 没有选择文件', 'error');
                return;
            }
            
            console.log('选择的文件:', {
                name: file.name,
                size: file.size,
                type: file.type
            });
            
            // 检查文件类型
            if (!file.type.startsWith('image/')) {
                showStatus('❌ 请选择图片文件！', 'error');
                return;
            }
            
            // 检查文件大小
            if (file.size > 4 * 1024 * 1024) {
                showStatus('❌ 文件太大！请选择小于4MB的图片', 'error');
                return;
            }
            
            // 保存文件
            uploadedFile = file;
            
            // 显示成功状态
            showStatus(`✅ 文件上传成功！
                文件名: ${file.name}
                大小: ${(file.size / 1024 / 1024).toFixed(2)} MB
                类型: ${file.type}`, 'success');
            
            // 显示预览
            const reader = new FileReader();
            reader.onload = function(e) {
                previewImg.src = e.target.result;
                previewDiv.style.display = 'block';
                console.log('预览图片已显示');
            };
            reader.onerror = function() {
                showStatus('❌ 图片读取失败', 'error');
            };
            reader.readAsDataURL(file);
        }
        
        function showStatus(message, type) {
            const statusDiv = document.getElementById('status');
            statusDiv.innerHTML = `<div class="status ${type}">${message.replace(/\n/g, '<br>')}</div>`;
        }
        
        function clearPreview() {
            uploadedFile = null;
            document.getElementById('preview').style.display = 'none';
            document.getElementById('previewImg').src = '';
            document.getElementById('fileInput').value = '';
            document.getElementById('fileInput2').value = '';
            showStatus('🗑️ 图片已清除', 'info');
        }
        
        // 页面加载时显示环境信息
        window.addEventListener('load', function() {
            console.log('=== 简单上传测试页面 ===');
            console.log('页面加载时间:', new Date().toLocaleString());
            console.log('浏览器:', navigator.userAgent);
            console.log('文件API支持:', {
                FileReader: !!window.FileReader,
                FormData: !!window.FormData,
                File: !!window.File
            });
            
            showStatus('📋 页面已加载，请选择图片文件进行测试', 'info');
        });
    </script>
</body>
</html>
