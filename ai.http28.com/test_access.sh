#!/bin/bash

# GPT-4o Image VIP 访问测试脚本
echo "🧪 GPT-4o Image VIP 访问测试"
echo "================================"

# 测试前端页面访问
echo "📱 测试前端页面访问..."
FRONTEND_STATUS=$(curl -s -o /dev/null -w "%{http_code}" http://************/gpt4o/)
if [ "$FRONTEND_STATUS" = "200" ]; then
    echo "✅ 前端页面访问正常 (HTTP $FRONTEND_STATUS)"
    echo "   访问地址: http://************/gpt4o/"
else
    echo "❌ 前端页面访问失败 (HTTP $FRONTEND_STATUS)"
fi

echo ""

# 测试API登录接口
echo "🔐 测试API登录接口..."
API_RESPONSE=$(curl -s -X POST -H "Content-Type: application/json" \
    -d '{"username":"admin","password":"admin123"}' \
    http://************/api/auth/login)

if echo "$API_RESPONSE" | grep -q "access_token"; then
    echo "✅ API登录接口正常"
    echo "   登录用户名: admin"
    echo "   登录密码: admin123"
else
    echo "❌ API登录接口失败"
    echo "   响应: $API_RESPONSE"
fi

echo ""

# 测试后端管理页面
echo "🔧 测试后端管理页面..."
BACKEND_STATUS=$(curl -s -o /dev/null -w "%{http_code}" http://************/)
if [ "$BACKEND_STATUS" = "200" ]; then
    echo "✅ 后端管理页面访问正常 (HTTP $BACKEND_STATUS)"
    echo "   访问地址: http://************/"
else
    echo "❌ 后端管理页面访问失败 (HTTP $BACKEND_STATUS)"
fi

echo ""

# 测试静态文件
echo "📄 测试静态文件..."
CSS_STATUS=$(curl -s -o /dev/null -w "%{http_code}" http://************/gpt4o/styles.css)
JS_STATUS=$(curl -s -o /dev/null -w "%{http_code}" http://************/gpt4o/script.js)

if [ "$CSS_STATUS" = "200" ] && [ "$JS_STATUS" = "200" ]; then
    echo "✅ 静态文件访问正常"
    echo "   CSS文件: HTTP $CSS_STATUS"
    echo "   JS文件: HTTP $JS_STATUS"
else
    echo "❌ 静态文件访问异常"
    echo "   CSS文件: HTTP $CSS_STATUS"
    echo "   JS文件: HTTP $JS_STATUS"
fi

echo ""
echo "🎉 测试完成！"
echo ""
echo "📋 访问信息总结:"
echo "   GPT-4o前端: http://************/gpt4o/"
echo "   后端管理: http://************/"
echo "   登录账号: admin / admin123"
echo ""
echo "🎨 功能特色:"
echo "   ✅ Discord风格界面"
echo "   ✅ 青绿色主题设计"
echo "   ✅ AI图像生成功能"
echo "   ✅ 图片上传和编辑"
echo "   ✅ 图像变体生成"
echo "   ✅ 用户认证系统"
echo "   ✅ 响应式布局"
echo ""
echo "🆕 新增功能:"
echo "   📷 图片上传功能"
echo "   ✏️ 图像编辑功能"
echo "   🔄 图像变体生成"
echo "   🎨 统一青绿色主题"
